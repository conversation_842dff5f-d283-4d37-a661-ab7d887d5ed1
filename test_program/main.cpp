#include <iostream>
#include <cstdlib>
#include <cstring>

// 故意引入null指针错误的测试程序
int main() {
    std::cout << "程序开始运行..." << std::endl;
    
    // 分配一些内存
    char* buffer = (char*)malloc(100);
    if (buffer) {
        strcpy(buffer, "Hello World");
        std::cout << "Buffer内容: " << buffer << std::endl;
    }
    
    // 故意设置null指针
    char* null_ptr = nullptr;
    
    std::cout << "即将访问null指针..." << std::endl;
    
    // 这里会导致段错误
    strcpy(null_ptr, "This will crash");
    
    std::cout << "程序不应该到达这里" << std::endl;
    
    free(buffer);
    return 0;
}
