# 优化1
1 .env文件增加TARGET_ARGS参数，用于指定中被调试的程序运行时参数
2 create_system_prompt方法的系统提示词增加当前系统的可用工具，让大模型可以决策选择哪些工具
3 大模型辅助gdb的第一阶段，需要让大模型了解当前项目结构，可以根据错误信息和代码知识库搜索工具，大致判断一个可能的错误，并加断点开始调试，这个过程可能持续多次调用，直到agent了解代码全貌。（通过系统提示词+工具，让大模型分析）
4 _debug_loop的调式轮次并不是10轮大模型API调用， 当gdb调试复现到错误才算一轮次debug调试，如果复现错误并准确判断了错误原因或者10轮次复现错误都没有找到错误原因，则结束调试。
5 每个轮次的gdb调试，都是由agent自动完成，模拟程序员调试的步骤：根据代码分析可能的原因，并在代码处加几个断点，运行测试程序，在断点处检查变量值、内存堆栈，判断是否是错误原因，否则可选择单步调试到下一行代码或继续执行代码等步骤。（通过系统提示词+工具，让大模型分析）

# 调试轮次
1.每个调试轮次都是重启新的服务进程TARGET_EXECUTABLE。如果配置了TEST_SCRIPT_PATH,则在服务进程启动后执行测试脚本。
2.服务进程运行终止（崩溃错误或正常退出）或测试脚本执行完毕，做为一个调试轮次。如果测试脚本执行完毕，可结束服务进程。
3."gdb_session:_read_output:185 - GDB命令执行超时 "，超时时间在.env中配置
4.解决前端问题： 调试输出区域，只输出了[15:00:08] undefined

# 问题
1.WEB UI点击知识库，内容是空的
2.调试历史，查看详情，后端错误："GET /api/memory/sessions/88f01319 HTTP/1.1" 404 Not Found
3.输出"测试脚本"执行的控制台输出
4.后端服务已经：stop_debug_session:386 - GDB调试会话已停止，WEB ui的调试按钮依然是“调试中”
# 优化2
1..env增加CODE_FILTER参数，用于指定构建知识库时被过滤的代码目录或文件，多个目录或文件用英文逗号隔开，支持.gitignore语法

# 问题
加载已经构建完毕的向量知识库后，self.knowledge_base.find_symbol("main")、self.knowledge_base.get_all_files()返回值都是空的

# 知识库构建问题
_parse_with_tree_sitter方法解析代码结构，没有正确提取符号和代码块。以代码/ddrive/github/gdb-test/src/main.cpp为例，提取的CodeSymbol对象如下：
CodeSymbol(name='atic std:', type='variable', file_path='/ddrive/github/gdb-test/src/main.cpp', line_number=13, column=0, signature=None, docstring=None, parent=None, children=[])
CodeSymbol(name='gnal) {\n    L', type='function', file_path='/ddrive/github/gdb-test/src/main.cpp', line_number=18, column=0, signature='nt signal)', docstring=None, parent=None, children=[])
CodeSymbol(name='ignalHandler);\n    ', type='function', file_path='/ddrive/github/gdb-test/src/main.cpp', line_number=31, column=0, signature='NT, signalHandler);\n    signal(SIGTERM, signalHandler);\n    signal(SIGPIPE, SIG_IGN); // 忽略SIGPIPE信号\n}\n\nvoid printBanner()', docstring=None, parent=None, children=[])等符号，没有提取到正确的方法名。
解决此问题，并用/ddrive/github/gdb-test/src/main.cpp代码测试解析的正确性。
break main.cpp:48

# 断点设置问题
在当前项目运行：python -m src.cli debug --fault-description "并发性能测试中，发生段错误，系统crash"， 执行到set_breakpoint方法，设置断点命令'break main.cpp:48'，返回的output为：'(gdb)\n&"handle SIGSEGV stop print\\n"\n~"Signal        Stop\\tPrint\\tPass to program\\tDescription\\n"\n~"SIGSEGV       Yes\\tYes\\tYes\\t\\tSegmentation fault\\n"\n^done' ，
导致_parse_breakpoint_id方法解析失败：设置断点失败.
解决此问题，运行： python -m src.cli debug --fault-description "并发性能测试中，发生段错误，系统crash" ,检查问题是否已经解决

# 解析问题
调用栈解析不成功，backtrace命令的控制台输出如下：&"backtrace\\n"\n~"#0  kvdb::ConfigManager::loadFromFile (this=0x5555555f8620 <kvdb::ConfigManager::getInstance()::instance>, config_file=\\"/ddrive/github/gdb-test/config/kvdb.conf\\") at /ddrive/github/gdb-test/src/config/config_manager.cpp:30\\n"\n~"#1  0x00005555555623d0 in kvdb::ConfigManager::loadFromCommandLine (this=0x5555555f8620 <kvdb::ConfigManager::getInstance()::instance>, argc=3, argv=0x7fffffffd898) at /ddrive/github/gdb-test/src/config/config_manager.cpp:86\\n"\n~"#2  0x000055555555db7f in main (argc=3, argv=0x7fffffffd898) at /ddrive/github/gdb-test/src/main.cpp:71\\n"\n^done\n(gdb) \n ，优化_parse_backtrace方法对backtrace命令的解析。

断点解析方法_parse_breakpoints优化，以下控制台输出未解析：&"info breakpoints\\n"\n~"Num     Type           Disp Enb Address            What\\n"\n~"1       breakpoint     keep y   0x0000555555561b17 in kvdb::ConfigManager::loadFromFile(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) at /ddrive/github/gdb-test/src/config/config_manager.cpp:30\\n"\n~"\\tbreakpoint already hit 1 time\\n"\n^done\n(gdb) \n

# 调试逻辑优化
每一轮调试的逻辑：
1.当程序停止（断点或异常信号）时，把上下文信息、内存变量值、堆栈信息等发送给大模型，让大模型判断是否已经找到错误。
2.如果是断点停止，则让大模型判断是否需要调用工具单步运行或者continue继续运行(解析大模型返回的tool_call，并调用相关方法)。
3.一轮调试的终止条件有以下几种：
  3.1 发生异常信号
  3.2 如果有测试脚本并且测试程序执行完毕
  3.3 单步调试超过20步（可配置）或者大模型没有返回任何工具调用。
  3.4 大模型判断当前的断点无法找到错误，需要重新运行程序
4.一轮调试如果未找到错误，则把本轮调试的内容让大模型进行总结，在下一轮调试时把前面几轮的调试总结做为上下文，避免大模型执行重复的调试。
5.优化大模型的提示词，更智能的实现以上功能。
6..env中配置的程序代码在main.cpp有个null地址的错误， 实现以上优化后可执行：python -m src.cli debug --fault-description "发生段错误，系统crash" ，测试是否能找到问题。

# 代码知识库构建支持文件过滤
1.在.env文件中增加CODE_FILTER过滤规则,支持目录、扩展名、正则，可参考.gitignore的语法。
2.多个过滤规则以逗号分开.
3.在.env中增加一个配置，用于表示在ai_agent.py的_execute_debug_round方法中，只有捕获到异常信号才结束本轮循环，而不仅判断round_result.get("next_step").

# 增加文件读取工具
1.增加文件读取工具，用于读取代码文件内容，并返回给大模型。
2.工具名为read_file，参数为文件路径，注册到工具列表中。

# 大模型输入超长
1.在.env中增加配置:LLM_MAX_INPUT=50K,用于限制大模型的最大输入长度。
2.session_completion方法优化：当检查到self.messages内消息的总长度超过LLM_MAX_INPUT时，保留系统提示词和最新的2个message，并把中间的message调用大模型方法(chat_completion)进行汇总，作为assistant的message插入到self.messages中间，同时说明这是本轮调试的历史记录汇总。

# 单轮调试中历史会话记录的交互优化
1.session_completion方法中，每次都把self.messages重新组织为2个Message，保留第1个系统提示词message，后面的message都拼接为一个user Message的content，content中保留原message的角色，比如：
- 1.user请求：content
- 2.assistant回复：content

2.然后并调用大模型方法(chat_completion)进行交互。

# 项目结构分析
1.在构建完项目代码知识库后，调用大模型分析一下这个项目的主要功能、目录结构，帮助大模型在后期的调试代码时对项目的理解。
2.项目分析的内容以json格式保存到data目录，在重建知识库时需要重新生成新的分析内容。
3.分析项目的具体实现方式可以参考augment插件分析项目代码的方式，比如支持agent方式，必要时让大模型调用read_file工具或知识库的工具读取代码文件内容。
4.在代码调试方法_run_debug_session_background，直接加载这个分析内容作为self.project_analysis。

# 调试界面UI优化
1.在切换到/debug 页面时，连接到后端服务器，并加载当前会话信息、填充界面的内容。
2.debug.html的断点管理区域改为断点列表，显示当前会话已经设置的断点，移除添加断点的操作。
3.debug.html移除：调试控制、 变量监控、调试日志。
4.调整debug.html的布局，左侧从上到下为：调试控制、调试状态、断点列表、调试输出。右侧增加一个与大模型交互展示的可滚动区域，以友好可读的模式实时显示ai_agent与大模型的往返交互内容（debug_logger的log_llm_request、log_llm_response及log_llm_error）。
5.debug.html的项目文件、代码编辑器区域移到knowledge.html的"知识库统计"下方，分左右布局：左边是项目文件列表、右边是代码编辑器。