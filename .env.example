# AI-GDB 配置文件示例
# 复制此文件为 .env 并填入实际配置

# ===== 项目配置 =====
# 项目代码库目录
PROJECT_CODE_DIR=/path/to/your/c_cpp_project
# GDB可执行文件路径
GDB_PATH=/usr/bin/gdb
# 目标可执行文件路径
TARGET_EXECUTABLE=/path/to/your/executable
# 目标程序运行时参数
TARGET_ARGS=--input data.txt --verbose
# 测试脚本路径
TEST_SCRIPT_PATH=/path/to/your/test_script.sh

# ===== LLM配置 =====
# LLM提供商 (openai)
LLM_PROVIDER=openai
# API密钥
LLM_API_KEY=your_api_key_here
# API基础URL (可选，用于自定义端点)
LLM_BASE_URL=https://api.openai.com/v1
# 模型名称
LLM_MODEL_NAME=gpt-4-turbo-preview
# 最大token数
LLM_MAX_TOKENS=4096
# 温度参数
LLM_TEMPERATURE=0.1

# ===== 向量数据库配置 =====
# 向量数据库类型 (chroma, faiss)
VECTOR_DB_TYPE=chroma
# 向量数据库存储路径
VECTOR_DB_PATH=./data/vector_db
# 嵌入模型名称
EMBEDDING_MODEL=all-MiniLM-L6-v2

# ===== 调试配置 =====
# 最大调试轮次
MAX_DEBUG_ROUNDS=10
# 每轮调试的最大步数
MAX_DEBUG_STEPS=20
# 断点超时时间(秒)
BREAKPOINT_TIMEOUT=30
# 是否启用内存泄露检测
ENABLE_MEMORY_LEAK_DETECTION=true
# 是否启用信号捕获
ENABLE_SIGNAL_CAPTURE=true

# ===== 日志配置 =====
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
# 日志文件路径
LOG_FILE_PATH=./logs/ai_gdb.log
# 是否启用控制台日志
ENABLE_CONSOLE_LOG=true
# 是否启用LLM调试日志（记录GDB执行、测试脚本执行、LLM API请求响应）
LLM_DEBUG=false

# ===== Web界面配置 =====
# Web服务器端口
WEB_PORT=8000
# Web服务器主机
WEB_HOST=0.0.0.0
# 是否启用Web界面
ENABLE_WEB_UI=true

# ===== 高级配置 =====
# 代码知识库重建间隔(小时)
KNOWLEDGE_BASE_REBUILD_INTERVAL=24
# 是否启用自动代码修复(预留)
ENABLE_AUTO_FIX=false
# 调试会话超时时间(分钟)
DEBUG_SESSION_TIMEOUT=60
