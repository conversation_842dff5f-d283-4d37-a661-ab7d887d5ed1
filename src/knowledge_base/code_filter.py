"""
代码过滤器模块
支持.gitignore风格的过滤规则
"""

import re
import fnmatch
from pathlib import Path
from typing import List, Optional
from loguru import logger


class CodeFilter:
    """代码过滤器，支持.gitignore风格的过滤规则"""
    
    def __init__(self, filter_rules: Optional[str] = None):
        """
        初始化代码过滤器
        
        Args:
            filter_rules: 过滤规则字符串，多个规则用逗号分隔
                         支持的规则格式：
                         - 目录: build/, Debug/, node_modules/
                         - 扩展名: *.o, *.so, *.pyc
                         - 文件名模式: test_*, *_backup
                         - 正则表达式: 以regex:开头，如 regex:.*\.tmp$
        """
        self.rules = []
        if filter_rules:
            self._parse_rules(filter_rules)
    
    def _parse_rules(self, filter_rules: str) -> None:
        """解析过滤规则"""
        rules = [rule.strip() for rule in filter_rules.split(',') if rule.strip()]
        
        for rule in rules:
            if not rule:
                continue
                
            # 正则表达式规则
            if rule.startswith('regex:'):
                regex_pattern = rule[6:]  # 去掉 'regex:' 前缀
                try:
                    compiled_regex = re.compile(regex_pattern)
                    self.rules.append({
                        'type': 'regex',
                        'pattern': regex_pattern,
                        'compiled': compiled_regex
                    })
                    logger.debug(f"添加正则表达式规则: {regex_pattern}")
                except re.error as e:
                    logger.warning(f"无效的正则表达式规则 '{regex_pattern}': {e}")
                    continue
            
            # 目录规则
            elif rule.endswith('/'):
                self.rules.append({
                    'type': 'directory',
                    'pattern': rule[:-1]  # 去掉末尾的 '/'
                })
                logger.debug(f"添加目录规则: {rule}")
            
            # 文件名模式规则（支持通配符）
            else:
                self.rules.append({
                    'type': 'pattern',
                    'pattern': rule
                })
                logger.debug(f"添加文件模式规则: {rule}")
    
    def should_exclude(self, file_path: Path, project_root: Path) -> bool:
        """
        判断文件是否应该被排除
        
        Args:
            file_path: 文件路径
            project_root: 项目根目录
            
        Returns:
            True if file should be excluded, False otherwise
        """
        if not self.rules:
            return False
            
        try:
            # 获取相对于项目根目录的路径
            relative_path = file_path.relative_to(project_root)
            relative_path_str = str(relative_path).replace('\\', '/')  # 统一使用 / 分隔符
            
            # 检查每个规则
            for rule in self.rules:
                if self._match_rule(rule, file_path, relative_path_str):
                    logger.debug(f"文件 {relative_path_str} 被规则 {rule['pattern']} 排除")
                    return True
                    
        except ValueError:
            # file_path 不在 project_root 下
            logger.warning(f"文件 {file_path} 不在项目根目录 {project_root} 下")
            return False
            
        return False
    
    def _match_rule(self, rule: dict, file_path: Path, relative_path_str: str) -> bool:
        """检查单个规则是否匹配"""
        rule_type = rule['type']
        pattern = rule['pattern']
        
        if rule_type == 'regex':
            # 正则表达式匹配
            return bool(rule['compiled'].search(relative_path_str))
        
        elif rule_type == 'directory':
            # 检查完整路径是否以目录模式开头
            if relative_path_str == pattern or relative_path_str.startswith(pattern + '/'):
                return True
                
        elif rule_type == 'pattern':
            # 文件名模式匹配
            file_name = file_path.name
            
            # 检查文件名是否匹配模式
            if fnmatch.fnmatch(file_name, pattern):
                return True
            
            # 检查完整相对路径是否匹配模式
            if fnmatch.fnmatch(relative_path_str, pattern):
                return True
        
        return False
    
    def get_rules_summary(self) -> List[str]:
        """获取规则摘要"""
        summary = []
        for rule in self.rules:
            rule_type = rule['type']
            pattern = rule['pattern']
            
            if rule_type == 'regex':
                summary.append(f"正则表达式: {pattern}")
            elif rule_type == 'directory':
                summary.append(f"目录: {pattern}/")
            elif rule_type == 'pattern':
                summary.append(f"文件模式: {pattern}")
                
        return summary
    
    def add_default_rules(self) -> None:
        """添加默认的过滤规则"""
        default_rules = [
            # 编译输出
            "*.o", "*.so", "*.a", "*.obj", "*.exe", "*.dll", "*.dylib",
            # 构建目录
            "build/", "Debug/", "Release/", "cmake-build-*/",
            # 版本控制
            ".git/", ".svn/", ".hg/",
            # IDE文件
            ".vscode/", ".idea/", "*.swp", "*.swo",
            # 临时文件
            "*.tmp", "*.temp", "*.log",
            # Python
            "__pycache__/", "*.pyc", "*.pyo", "*.pyd",
            # Node.js
            "node_modules/",
            # 其他
            ".DS_Store", "Thumbs.db"
        ]
        
        for rule in default_rules:
            if rule.endswith('/'):
                self.rules.append({
                    'type': 'directory',
                    'pattern': rule[:-1]
                })
            else:
                self.rules.append({
                    'type': 'pattern',
                    'pattern': rule
                })
        
        logger.info(f"添加了 {len(default_rules)} 个默认过滤规则")


def create_code_filter(filter_rules: Optional[str] = None, 
                      include_defaults: bool = True) -> CodeFilter:
    """
    创建代码过滤器
    
    Args:
        filter_rules: 自定义过滤规则
        include_defaults: 是否包含默认规则
        
    Returns:
        CodeFilter实例
    """
    code_filter = CodeFilter(filter_rules)
    
    if include_defaults:
        code_filter.add_default_rules()
    
    return code_filter
