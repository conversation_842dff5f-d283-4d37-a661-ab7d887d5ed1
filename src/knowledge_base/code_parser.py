"""
C/C++代码解析器
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass
from loguru import logger

from .code_filter import CodeFilter, create_code_filter

try:
    import tree_sitter
    from tree_sitter import Language, Parser
    import tree_sitter_c as tsc
    import tree_sitter_cpp as tscpp
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False
    logger.warning("tree-sitter不可用，将使用正则表达式解析")


@dataclass
class CodeSymbol:
    """代码符号"""
    name: str
    type: str  # function, variable, struct, class, macro, etc.
    file_path: str
    line_number: int
    column: int
    signature: Optional[str] = None
    docstring: Optional[str] = None
    parent: Optional[str] = None
    children: List[str] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []


@dataclass
class CodeChunk:
    """代码块"""
    content: str
    file_path: str
    start_line: int
    end_line: int
    symbols: List[CodeSymbol]
    dependencies: List[str]  # 依赖的其他文件或符号


class CodeParser:
    """C/C++代码解析器"""
    
    def __init__(self, config=None):
        self.config = config
        self.c_extensions = {'.c', '.h'}
        self.cpp_extensions = {'.cpp', '.cxx', '.cc', '.hpp', '.hxx', '.hh'}
        self.all_extensions = self.c_extensions | self.cpp_extensions

        # 初始化代码过滤器
        self.code_filter = None
        if config and hasattr(config, 'code_filter') and config.code_filter:
            self.code_filter = create_code_filter(config.code_filter, include_defaults=True)
            logger.info(f"代码过滤器已启用，规则: {self.code_filter.get_rules_summary()}")
        else:
            # 即使没有自定义规则，也使用默认规则
            self.code_filter = create_code_filter(None, include_defaults=True)
            logger.info("使用默认代码过滤规则")

        if TREE_SITTER_AVAILABLE:
            self._init_tree_sitter()
        else:
            self.parser = None
            self.c_language = None
            self.cpp_language = None
    
    def _init_tree_sitter(self):
        """初始化tree-sitter解析器"""
        try:
            # 构建语言 - 修复版本兼容性问题
            # 新版本的tree-sitter Language构造函数只需要一个参数
            self.c_language = Language(tsc.language())
            self.cpp_language = Language(tscpp.language())

            # 创建解析器
            self.parser = Parser()

            logger.info("Tree-sitter解析器初始化成功")
        except Exception as e:
            logger.error(f"Tree-sitter初始化失败: {e}")
            logger.warning("将回退到正则表达式解析模式")
            self.parser = None
            self.c_language = None
            self.cpp_language = None
    
    def parse_project(self, project_dir: Path) -> List[CodeChunk]:
        """解析整个项目"""
        logger.info(f"开始解析项目: {project_dir}")
        
        code_files = self._find_code_files(project_dir)
        logger.info(f"找到 {len(code_files)} 个代码文件")
        
        chunks = []
        for file_path in code_files:
            try:
                file_chunks = self.parse_file(file_path)
                chunks.extend(file_chunks)
                logger.debug(f"解析文件 {file_path}: {len(file_chunks)} 个代码块")
            except Exception as e:
                logger.error(f"解析文件 {file_path} 失败: {e}")
        
        logger.info(f"项目解析完成，共 {len(chunks)} 个代码块")
        return chunks
    
    def _find_code_files(self, project_dir: Path) -> List[Path]:
        """查找项目中的代码文件"""
        code_files = []
        excluded_count = 0

        for root, dirs, files in os.walk(project_dir):
            root_path = Path(root)

            # 使用过滤器过滤目录
            if self.code_filter:
                # 过滤目录列表，移除被排除的目录
                filtered_dirs = []
                for d in dirs:
                    dir_path = root_path / d
                    if not self.code_filter.should_exclude(dir_path, project_dir):
                        filtered_dirs.append(d)
                    else:
                        logger.debug(f"目录被过滤器排除: {dir_path.relative_to(project_dir)}")
                dirs[:] = filtered_dirs
            else:
                # 原有的目录过滤逻辑（作为后备）
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in {
                    'build', 'cmake-build-debug', 'cmake-build-release',
                    'Debug', 'Release', 'obj', 'bin', 'target'
                }]

            for file in files:
                file_path = Path(root) / file

                # 检查文件扩展名
                if file_path.suffix.lower() not in self.all_extensions:
                    continue

                # 使用过滤器检查文件
                if self.code_filter and self.code_filter.should_exclude(file_path, project_dir):
                    excluded_count += 1
                    continue

                code_files.append(file_path)

        logger.info(f"找到 {len(code_files)} 个代码文件，排除了 {excluded_count} 个文件")
        return code_files
    
    def parse_file(self, file_path: Path) -> List[CodeChunk]:
        """解析单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            logger.error(f"读取文件 {file_path} 失败: {e}")
            return []
        
        if self.parser and TREE_SITTER_AVAILABLE:
            return self._parse_with_tree_sitter(file_path, content)
        else:
            return self._parse_with_regex(file_path, content)
    
    def _parse_with_tree_sitter(self, file_path: Path, content: str) -> List[CodeChunk]:
        """使用tree-sitter解析"""
        try:
            # 根据文件扩展名选择语言
            if file_path.suffix.lower() in self.c_extensions:
                self.parser.language = self.c_language
            else:
                self.parser.language = self.cpp_language

            # 将内容转换为字节，确保编码正确
            content_bytes = content.encode('utf-8')

            # 解析代码
            tree = self.parser.parse(content_bytes)

            # 提取符号和代码块
            symbols = self._extract_symbols_tree_sitter_fixed(tree.root_node, content, str(file_path))
            chunks = self._create_chunks_from_symbols(symbols, content, str(file_path))

            return chunks
        except Exception as e:
            logger.error(f"tree-sitter解析失败: {e}")
            # 回退到正则表达式解析
            return self._parse_with_regex(file_path, content)
    
    def _extract_symbols_tree_sitter(self, node, content: str, file_path: str) -> List[CodeSymbol]:
        """使用tree-sitter提取符号"""
        symbols = []

        def traverse(node):
            try:
                # 函数定义
                if node.type == 'function_definition':
                    func_name = self._get_function_name(node, content)
                    if func_name and func_name.strip():
                        symbol = CodeSymbol(
                            name=func_name.strip(),
                            type='function',
                            file_path=file_path,
                            line_number=node.start_point[0] + 1,
                            column=node.start_point[1],
                            signature=self._get_function_signature(node, content)
                        )
                        symbols.append(symbol)

                # 函数声明（不包含函数体）
                elif node.type == 'declaration':
                    # 检查是否是函数声明
                    func_name = self._get_function_declaration_name(node, content)
                    if func_name and func_name.strip():
                        symbol = CodeSymbol(
                            name=func_name.strip(),
                            type='function_declaration',
                            file_path=file_path,
                            line_number=node.start_point[0] + 1,
                            column=node.start_point[1],
                            signature=self._get_declaration_signature(node, content)
                        )
                        symbols.append(symbol)
                    else:
                        # 变量声明
                        var_names = self._get_variable_names(node, content)
                        for var_name in var_names:
                            if var_name and var_name.strip():
                                symbol = CodeSymbol(
                                    name=var_name.strip(),
                                    type='variable',
                                    file_path=file_path,
                                    line_number=node.start_point[0] + 1,
                                    column=node.start_point[1]
                                )
                                symbols.append(symbol)

                # 结构体定义
                elif node.type in ['struct_specifier', 'class_specifier']:
                    struct_name = self._get_struct_name(node, content)
                    if struct_name and struct_name.strip():
                        symbol = CodeSymbol(
                            name=struct_name.strip(),
                            type='struct' if node.type == 'struct_specifier' else 'class',
                            file_path=file_path,
                            line_number=node.start_point[0] + 1,
                            column=node.start_point[1]
                        )
                        symbols.append(symbol)

                # 枚举定义
                elif node.type == 'enum_specifier':
                    enum_name = self._get_enum_name(node, content)
                    if enum_name and enum_name.strip():
                        symbol = CodeSymbol(
                            name=enum_name.strip(),
                            type='enum',
                            file_path=file_path,
                            line_number=node.start_point[0] + 1,
                            column=node.start_point[1]
                        )
                        symbols.append(symbol)

                # 递归遍历子节点
                for child in node.children:
                    traverse(child)

            except Exception as e:
                logger.debug(f"解析节点时出错: {e}, 节点类型: {node.type}")

        traverse(node)
        return symbols

    def _extract_symbols_tree_sitter_fixed(self, node, content: str, file_path: str) -> List[CodeSymbol]:
        """使用tree-sitter提取符号（修复版本）"""
        symbols = []
        content_lines = content.split('\n')

        def traverse(node):
            try:
                # 函数定义
                if node.type == 'function_definition':
                    func_info = self._extract_function_info_fixed(node, content, content_lines)
                    if func_info:
                        symbol = CodeSymbol(
                            name=func_info['name'],
                            type='function',
                            file_path=file_path,
                            line_number=func_info['line_number'],
                            column=func_info['column'],
                            signature=func_info['signature']
                        )
                        symbols.append(symbol)

                # 结构体和类定义
                elif node.type in ['struct_specifier', 'class_specifier']:
                    struct_info = self._extract_struct_info_fixed(node, content, content_lines)
                    if struct_info:
                        symbol = CodeSymbol(
                            name=struct_info['name'],
                            type='struct' if node.type == 'struct_specifier' else 'class',
                            file_path=file_path,
                            line_number=struct_info['line_number'],
                            column=struct_info['column']
                        )
                        symbols.append(symbol)

                # 递归遍历子节点
                for child in node.children:
                    traverse(child)

            except Exception as e:
                logger.debug(f"解析节点时出错: {e}, 节点类型: {node.type}")

        traverse(node)
        return symbols

    def _extract_function_info_fixed(self, node, content: str, content_lines: List[str]) -> Optional[dict]:
        """提取函数信息（修复版本）"""
        try:
            line_number = node.start_point[0] + 1
            column = node.start_point[1]

            # 获取函数所在行的内容
            if line_number <= len(content_lines):
                line_content = content_lines[line_number - 1]

                # 使用正则表达式从行内容中提取函数名
                import re
                # 匹配函数定义模式
                func_pattern = r'^\s*(?:static\s+|inline\s+|virtual\s+)*(?:const\s+)*(\w+(?:\s*\*)*)\s+(\w+)\s*\('
                match = re.search(func_pattern, line_content)

                if match:
                    return_type = match.group(1).strip()
                    func_name = match.group(2).strip()

                    # 获取函数签名（从当前行开始到第一个大括号）
                    signature_lines = []
                    for i in range(line_number - 1, min(line_number + 5, len(content_lines))):
                        signature_lines.append(content_lines[i])
                        if '{' in content_lines[i]:
                            break

                    signature = ' '.join(signature_lines).split('{')[0].strip()

                    return {
                        'name': func_name,
                        'line_number': line_number,
                        'column': column,
                        'signature': signature
                    }

                # 如果正则匹配失败，尝试简单的启发式方法
                # 查找第一个标识符后跟括号的模式
                words = line_content.split()
                for i, word in enumerate(words):
                    if '(' in word and word.replace('(', '').replace(')', '').isidentifier():
                        func_name = word.split('(')[0]
                        if func_name and func_name.isidentifier():
                            return {
                                'name': func_name,
                                'line_number': line_number,
                                'column': column,
                                'signature': line_content.strip()
                            }

        except Exception as e:
            logger.debug(f"提取函数信息时出错: {e}")

        return None

    def _extract_struct_info_fixed(self, node, content: str, content_lines: List[str]) -> Optional[dict]:
        """提取结构体信息（修复版本）"""
        try:
            line_number = node.start_point[0] + 1
            column = node.start_point[1]

            # 获取结构体所在行的内容
            if line_number <= len(content_lines):
                line_content = content_lines[line_number - 1]

                # 使用正则表达式提取结构体名
                import re
                struct_pattern = r'^\s*(struct|class)\s+(\w+)'
                match = re.search(struct_pattern, line_content)

                if match:
                    struct_name = match.group(2)
                    return {
                        'name': struct_name,
                        'line_number': line_number,
                        'column': column
                    }

        except Exception as e:
            logger.debug(f"提取结构体信息时出错: {e}")

        return None
    
    def _parse_with_regex(self, file_path: Path, content: str) -> List[CodeChunk]:
        """使用正则表达式解析"""
        symbols = self._extract_symbols_regex(content, str(file_path))
        chunks = self._create_chunks_from_symbols(symbols, content, str(file_path))
        return chunks
    
    def _extract_symbols_regex(self, content: str, file_path: str) -> List[CodeSymbol]:
        """使用正则表达式提取符号"""
        symbols = []
        lines = content.split('\n')
        
        # 函数定义正则
        func_pattern = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_*\s]*\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*\{?', re.MULTILINE)
        
        # 结构体定义正则
        struct_pattern = re.compile(r'^\s*(struct|class|typedef\s+struct)\s+([a-zA-Z_][a-zA-Z0-9_]*)', re.MULTILINE)
        
        # 变量声明正则
        var_pattern = re.compile(r'^\s*[a-zA-Z_][a-zA-Z0-9_*\s]*\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[;=]', re.MULTILINE)
        
        # 查找函数
        for match in func_pattern.finditer(content):
            line_num = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(1),
                type='function',
                file_path=file_path,
                line_number=line_num,
                column=match.start() - content.rfind('\n', 0, match.start()) - 1,
                signature=match.group(0).strip()
            )
            symbols.append(symbol)
        
        # 查找结构体
        for match in struct_pattern.finditer(content):
            line_num = content[:match.start()].count('\n') + 1
            symbol = CodeSymbol(
                name=match.group(2),
                type='struct' if 'struct' in match.group(1) else 'class',
                file_path=file_path,
                line_number=line_num,
                column=match.start() - content.rfind('\n', 0, match.start()) - 1
            )
            symbols.append(symbol)
        
        return symbols
    
    def _create_chunks_from_symbols(self, symbols: List[CodeSymbol], content: str, file_path: str) -> List[CodeChunk]:
        """从符号创建代码块"""
        chunks = []
        lines = content.split('\n')
        
        # 为每个函数创建代码块
        for symbol in symbols:
            if symbol.type == 'function':
                start_line = symbol.line_number - 1
                end_line = self._find_function_end(lines, start_line)
                
                chunk_content = '\n'.join(lines[start_line:end_line + 1])
                dependencies = self._extract_dependencies(chunk_content)
                
                chunk = CodeChunk(
                    content=chunk_content,
                    file_path=file_path,
                    start_line=start_line + 1,
                    end_line=end_line + 1,
                    symbols=[symbol],
                    dependencies=dependencies
                )
                chunks.append(chunk)
        
        # 如果没有函数，创建整个文件的代码块
        if not any(s.type == 'function' for s in symbols):
            dependencies = self._extract_dependencies(content)
            chunk = CodeChunk(
                content=content,
                file_path=file_path,
                start_line=1,
                end_line=len(lines),
                symbols=symbols,
                dependencies=dependencies
            )
            chunks.append(chunk)
        
        return chunks
    
    def _find_function_end(self, lines: List[str], start_line: int) -> int:
        """查找函数结束行"""
        brace_count = 0
        in_function = False
        
        for i in range(start_line, len(lines)):
            line = lines[i]
            
            for char in line:
                if char == '{':
                    brace_count += 1
                    in_function = True
                elif char == '}':
                    brace_count -= 1
                    if in_function and brace_count == 0:
                        return i
        
        # 如果没有找到结束大括号，返回文件末尾
        return len(lines) - 1
    
    def _extract_dependencies(self, content: str) -> List[str]:
        """提取依赖关系"""
        dependencies = []
        
        # 提取#include
        include_pattern = re.compile(r'#include\s*[<"]([^>"]+)[>"]')
        for match in include_pattern.finditer(content):
            dependencies.append(match.group(1))
        
        return dependencies
    
    def _get_function_name(self, node, content: str) -> Optional[str]:
        """获取函数名"""
        try:
            # 查找函数声明器
            for child in node.children:
                if child.type == 'function_declarator':
                    # 在函数声明器中查找标识符
                    identifier_node = self._find_identifier_in_declarator(child)
                    if identifier_node:
                        name = content[identifier_node.start_byte:identifier_node.end_byte]
                        return name.strip()

            # 如果没有找到函数声明器，直接在子节点中查找标识符
            for child in node.children:
                if child.type == 'identifier':
                    name = content[child.start_byte:child.end_byte]
                    return name.strip()
        except Exception as e:
            logger.debug(f"获取函数名时出错: {e}")
        return None

    def _find_identifier_in_declarator(self, declarator_node):
        """在声明器中递归查找标识符节点"""
        try:
            # 直接查找标识符
            for child in declarator_node.children:
                if child.type == 'identifier':
                    return child
                # 递归处理嵌套的声明器
                elif child.type in ['parenthesized_declarator', 'pointer_declarator']:
                    result = self._find_identifier_in_declarator(child)
                    if result:
                        return result
        except Exception as e:
            logger.debug(f"在声明器中查找标识符时出错: {e}")
        return None

    def _get_function_declaration_name(self, node, content: str) -> Optional[str]:
        """获取函数声明的名称"""
        try:
            # 检查是否包含函数声明器
            for child in node.children:
                if child.type == 'function_declarator':
                    return self._extract_identifier_from_declarator(child, content)
        except Exception as e:
            logger.debug(f"获取函数声明名时出错: {e}")
        return None

    def _extract_identifier_from_declarator(self, node, content: str) -> Optional[str]:
        """从声明器中提取标识符"""
        try:
            identifier_node = self._find_identifier_in_declarator(node)
            if identifier_node:
                name = content[identifier_node.start_byte:identifier_node.end_byte]
                return name.strip()
        except Exception as e:
            logger.debug(f"从声明器提取标识符时出错: {e}")
        return None

    def _get_function_signature(self, node, content: str) -> str:
        """获取函数签名"""
        try:
            # 获取整个函数定义的文本，但只取到第一个大括号之前
            full_text = content[node.start_byte:node.end_byte]
            # 找到第一个大括号的位置
            brace_pos = full_text.find('{')
            if brace_pos != -1:
                signature = full_text[:brace_pos].strip()
            else:
                signature = full_text.strip()

            # 清理多余的空白字符
            signature = ' '.join(signature.split())
            return signature
        except Exception as e:
            logger.debug(f"获取函数签名时出错: {e}")
            return ""

    def _get_declaration_signature(self, node, content: str) -> str:
        """获取声明签名"""
        try:
            signature = content[node.start_byte:node.end_byte].strip()
            # 清理多余的空白字符
            signature = ' '.join(signature.split())
            return signature
        except Exception as e:
            logger.debug(f"获取声明签名时出错: {e}")
            return ""

    def _get_struct_name(self, node, content: str) -> Optional[str]:
        """获取结构体名"""
        try:
            for child in node.children:
                if child.type == 'type_identifier':
                    name = content[child.start_byte:child.end_byte]
                    return name.strip()
        except Exception as e:
            logger.debug(f"获取结构体名时出错: {e}")
        return None

    def _get_enum_name(self, node, content: str) -> Optional[str]:
        """获取枚举名"""
        try:
            for child in node.children:
                if child.type == 'type_identifier':
                    name = content[child.start_byte:child.end_byte]
                    return name.strip()
        except Exception as e:
            logger.debug(f"获取枚举名时出错: {e}")
        return None

    def _get_variable_names(self, node, content: str) -> List[str]:
        """获取变量名"""
        names = []
        try:
            # 遍历声明中的所有声明器
            for child in node.children:
                if child.type == 'init_declarator':
                    # 在初始化声明器中查找标识符
                    name = self._extract_identifier_from_declarator(child, content)
                    if name:
                        names.append(name)
                elif child.type == 'identifier':
                    # 直接的标识符
                    name = content[child.start_byte:child.end_byte]
                    names.append(name.strip())
        except Exception as e:
            logger.debug(f"获取变量名时出错: {e}")
        return names
