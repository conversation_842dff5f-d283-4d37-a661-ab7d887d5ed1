"""
项目分析器模块
用于分析项目的主要功能、目录结构等信息
"""

import json
import os
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger
from datetime import datetime


class ProjectAnalyzer:
    """项目分析器"""
    
    def __init__(self, config, knowledge_base, llm_client=None, tool_manager=None):
        self.config = config
        self.knowledge_base = knowledge_base
        self.llm_client = llm_client
        self.tool_manager = tool_manager
        self.analysis_file_path = Path("data") / "project_analysis.json"
        
    async def analyze_project(self) -> Dict[str, Any]:
        """分析项目并生成分析报告"""
        try:
            logger.info("开始分析项目结构和功能...")
            
            # 1. 收集基础项目信息
            basic_info = await self._collect_basic_info()
            
            # 2. 分析目录结构
            directory_structure = await self._analyze_directory_structure()
            
            # 3. 分析代码统计信息
            code_statistics = await self._analyze_code_statistics()
            
            # 4. 分析主要功能模块
            main_modules = await self._analyze_main_modules()
            
            # 5. 分析依赖关系
            dependencies = await self._analyze_dependencies()
            
            # 6. 使用LLM进行综合分析（如果可用）
            llm_analysis = None
            if self.llm_client:
                llm_analysis = await self._llm_comprehensive_analysis(
                    basic_info, directory_structure, code_statistics, main_modules, dependencies
                )
            
            # 7. 组装分析结果
            analysis_result = {
                "timestamp": datetime.now().isoformat(),
                "project_path": str(self.config.project_code_dir),
                "basic_info": basic_info,
                "directory_structure": directory_structure,
                "code_statistics": code_statistics,
                "main_modules": main_modules,
                "dependencies": dependencies,
                "llm_analysis": llm_analysis,
                "analysis_version": "1.0"
            }
            
            # 8. 保存分析结果
            await self._save_analysis(analysis_result)
            
            logger.info("项目分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"项目分析失败: {e}")
            raise
    
    async def _collect_basic_info(self) -> Dict[str, Any]:
        """收集基础项目信息"""
        try:
            project_dir = Path(self.config.project_code_dir)
            
            basic_info = {
                "project_name": project_dir.name,
                "project_path": str(project_dir),
                "target_executable": str(self.config.target_executable) if self.config.target_executable else None,
                "target_args": self.config.target_args,
                "gdb_path": str(self.config.gdb_path),
                "project_size": 0,
                "file_count": 0,
                "languages": []
            }
            
            # 统计项目大小和文件数量
            total_size = 0
            file_count = 0
            languages = set()
            
            for file_path in project_dir.rglob("*"):
                if file_path.is_file():
                    file_count += 1
                    total_size += file_path.stat().st_size
                    
                    # 识别编程语言
                    suffix = file_path.suffix.lower()
                    if suffix in {'.c', '.h'}:
                        languages.add("C")
                    elif suffix in {'.cpp', '.cxx', '.cc', '.hpp', '.hxx', '.hh'}:
                        languages.add("C++")
                    elif suffix in {'.py'}:
                        languages.add("Python")
                    elif suffix in {'.js', '.ts'}:
                        languages.add("JavaScript/TypeScript")
                    elif suffix in {'.java'}:
                        languages.add("Java")
                    elif suffix in {'.go'}:
                        languages.add("Go")
                    elif suffix in {'.rs'}:
                        languages.add("Rust")
            
            basic_info["project_size"] = total_size
            basic_info["file_count"] = file_count
            basic_info["languages"] = list(languages)
            
            return basic_info
            
        except Exception as e:
            logger.error(f"收集基础项目信息失败: {e}")
            return {"error": str(e)}
    
    async def _analyze_directory_structure(self) -> Dict[str, Any]:
        """分析目录结构"""
        try:
            project_dir = Path(self.config.project_code_dir)
            
            def build_tree(path: Path, max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
                if current_depth >= max_depth:
                    return {"name": path.name, "type": "directory", "truncated": True}
                
                if path.is_file():
                    return {
                        "name": path.name,
                        "type": "file",
                        "size": path.stat().st_size,
                        "extension": path.suffix
                    }
                elif path.is_dir():
                    children = []
                    try:
                        for child in sorted(path.iterdir()):
                            if not child.name.startswith('.'):  # 跳过隐藏文件
                                children.append(build_tree(child, max_depth, current_depth + 1))
                    except PermissionError:
                        pass
                    
                    return {
                        "name": path.name,
                        "type": "directory",
                        "children": children
                    }
            
            directory_tree = build_tree(project_dir)
            
            # 统计目录信息
            dir_stats = {
                "total_directories": 0,
                "total_files": 0,
                "main_directories": []
            }
            
            # 找出主要目录
            for item in project_dir.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    file_count = sum(1 for _ in item.rglob("*") if _.is_file())
                    dir_stats["main_directories"].append({
                        "name": item.name,
                        "file_count": file_count
                    })
            
            return {
                "directory_tree": directory_tree,
                "statistics": dir_stats
            }
            
        except Exception as e:
            logger.error(f"分析目录结构失败: {e}")
            return {"error": str(e)}
    
    async def _analyze_code_statistics(self) -> Dict[str, Any]:
        """分析代码统计信息"""
        try:
            # 从知识库获取统计信息
            kb_stats = self.knowledge_base.get_statistics()
            
            # 扩展统计信息
            code_stats = {
                "knowledge_base_stats": kb_stats,
                "file_types": {},
                "function_count": 0,
                "class_count": 0,
                "variable_count": 0
            }
            
            # 统计符号类型
            if "symbol_types" in kb_stats:
                for symbol_type, count in kb_stats["symbol_types"].items():
                    if symbol_type == "function":
                        code_stats["function_count"] = count
                    elif symbol_type == "class":
                        code_stats["class_count"] = count
                    elif symbol_type == "variable":
                        code_stats["variable_count"] = count
            
            # 统计文件类型
            all_files = self.knowledge_base.get_all_files()
            for file_path in all_files:
                ext = Path(file_path).suffix.lower()
                if ext:
                    code_stats["file_types"][ext] = code_stats["file_types"].get(ext, 0) + 1
            
            return code_stats
            
        except Exception as e:
            logger.error(f"分析代码统计信息失败: {e}")
            return {"error": str(e)}
    
    async def _analyze_main_modules(self) -> Dict[str, Any]:
        """分析主要功能模块"""
        try:
            modules = {
                "main_functions": [],
                "important_files": [],
                "key_symbols": []
            }
            
            # 查找main函数
            main_symbols = self.knowledge_base.find_symbol("main")
            for symbol in main_symbols[:5]:  # 最多5个main函数
                modules["main_functions"].append({
                    "file": symbol.file_path,
                    "line": symbol.line_number,
                    "signature": getattr(symbol, 'signature', '')
                })
            
            # 查找重要文件（基于文件大小和符号数量）
            all_files = self.knowledge_base.get_all_files()
            file_importance = []
            
            for file_path in all_files[:20]:  # 限制处理文件数量
                file_chunks = self.knowledge_base.get_file_context(file_path)
                symbol_count = sum(len(chunk.symbols) for chunk in file_chunks)
                
                if symbol_count > 0:
                    file_importance.append({
                        "file": file_path,
                        "symbol_count": symbol_count,
                        "chunk_count": len(file_chunks)
                    })
            
            # 按符号数量排序
            file_importance.sort(key=lambda x: x["symbol_count"], reverse=True)
            modules["important_files"] = file_importance[:10]
            
            # 查找关键符号（函数、类等）
            symbol_types = ["function", "class", "struct"]
            for symbol_type in symbol_types:
                symbols = self.knowledge_base.find_symbols_by_type(symbol_type)
                modules["key_symbols"].extend([
                    {
                        "name": symbol.name,
                        "type": symbol.type,
                        "file": symbol.file_path,
                        "line": symbol.line_number
                    }
                    for symbol in symbols[:10]  # 每种类型最多10个
                ])
            
            return modules
            
        except Exception as e:
            logger.error(f"分析主要功能模块失败: {e}")
            return {"error": str(e)}
    
    async def _analyze_dependencies(self) -> Dict[str, Any]:
        """分析依赖关系"""
        try:
            dependencies = {
                "internal_dependencies": [],
                "external_libraries": [],
                "include_patterns": []
            }
            
            # 分析内部依赖关系
            # 这里可以通过分析代码块的依赖关系来实现
            # 由于时间限制，先返回基础结构
            
            return dependencies
            
        except Exception as e:
            logger.error(f"分析依赖关系失败: {e}")
            return {"error": str(e)}

    async def _llm_comprehensive_analysis(self, basic_info: Dict, directory_structure: Dict,
                                        code_statistics: Dict, main_modules: Dict,
                                        dependencies: Dict) -> Optional[Dict[str, Any]]:
        """使用LLM进行综合项目分析"""
        try:
            if not self.llm_client:
                return None

            # 开始新的LLM会话
            system_prompt = """你是一个专业的代码分析专家，擅长分析C/C++项目的结构和功能。
请基于提供的项目信息，生成一个全面的项目分析报告，包括：

1. 项目概述：项目的主要目的和功能
2. 架构分析：项目的整体架构和设计模式
3. 核心模块：识别和描述主要的功能模块
4. 技术栈：使用的技术和工具
5. 潜在问题：可能存在的问题和改进建议
6. 调试建议：针对调试的具体建议

请以JSON格式返回分析结果，包含以下字段：
- overview: 项目概述
- directory_structure: 项目的根目录路径，各子目录的文件路径及功能描述
- architecture: 架构分析
- core_modules: 核心模块列表
- tech_stack: 技术栈
- potential_issues: 潜在问题列表
- debugging_suggestions: 调试建议列表

确保分析结果准确、实用，特别关注对调试有帮助的信息。"""

            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(
                basic_info, directory_structure, code_statistics, main_modules, dependencies
            )

            self.llm_client.start_new_session(system_prompt)

            # 获取工具列表（如果有工具管理器）
            tools = None
            if self.tool_manager:
                tools = self.tool_manager.get_tools_schema()

            while True:
                # 调用LLM进行分析
                response = await self.llm_client.session_completion(analysis_prompt, tools)
                if response.tool_calls:
                    for tool_call_data in response.tool_calls:
                        result = await self.llm_client.call_tool(tool_call_data)
                    continue

                # 解析LLM响应
                try:
                    # 尝试解析JSON响应
                    llm_result = json.loads(response.content)
                    return {
                        "llm_analysis": llm_result,
                        #"raw_response": response.content,
                        "usage": response.usage,
                        "response_time": response.response_time
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，返回原始文本
                    return {
                        "llm_analysis": response.content,
                        #"raw_response": response.content,
                        "usage": response.usage,
                        "response_time": response.response_time
                    }

        except Exception as e:
            logger.error(f"LLM综合分析失败: {e}")
            return {"error": str(e)}

    def _build_analysis_prompt(self, basic_info: Dict, directory_structure: Dict,
                             code_statistics: Dict, main_modules: Dict,
                             dependencies: Dict) -> str:
        """构建分析提示词"""
        prompt = f"""请分析以下C/C++项目的信息：

## 基础信息
项目名称: {basic_info.get('project_name', 'Unknown')}
项目路径: {basic_info.get('project_path', 'Unknown')}
目标可执行文件: {basic_info.get('target_executable', 'None')}
项目大小: {basic_info.get('project_size', 0)} 字节
文件数量: {basic_info.get('file_count', 0)}
编程语言: {', '.join(basic_info.get('languages', []))}

## 代码统计
总代码块: {code_statistics.get('knowledge_base_stats', {}).get('total_chunks', 0)}
总符号数: {code_statistics.get('knowledge_base_stats', {}).get('total_symbols', 0)}
函数数量: {code_statistics.get('function_count', 0)}
类数量: {code_statistics.get('class_count', 0)}
变量数量: {code_statistics.get('variable_count', 0)}

## 主要模块
主函数位置: {json.dumps(main_modules.get('main_functions', []), indent=2)}
重要文件: {json.dumps(main_modules.get('important_files', [])[:5], indent=2)}

## 目录结构
主要目录: {json.dumps(directory_structure.get('statistics', {}).get('main_directories', []), indent=2)}

请基于以上信息，分析这个项目的主要功能、架构设计、核心模块，并提供调试建议。
如果需要查看具体的代码文件内容，可以使用read_file工具。
如果需要搜索特定的代码片段，可以使用search_code工具。"""

        return prompt

    async def _save_analysis(self, analysis_result: Dict[str, Any]) -> None:
        """保存分析结果到文件"""
        try:
            # 确保data目录存在
            self.analysis_file_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存为JSON文件
            with open(self.analysis_file_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)

            logger.info(f"项目分析结果已保存到: {self.analysis_file_path}")

        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            raise

    def load_analysis(self) -> Optional[Dict[str, Any]]:
        """加载已保存的分析结果"""
        try:
            if not self.analysis_file_path.exists():
                logger.info("未找到已保存的项目分析文件")
                return None

            with open(self.analysis_file_path, 'r', encoding='utf-8') as f:
                analysis_result = json.load(f)

            logger.info(f"成功加载项目分析结果: {self.analysis_file_path}")
            return analysis_result

        except Exception as e:
            logger.error(f"加载项目分析结果失败: {e}")
            return None

    def get_analysis_file_path(self) -> Path:
        """获取分析文件路径"""
        return self.analysis_file_path

    def is_analysis_outdated(self) -> bool:
        """检查分析结果是否过期（基于知识库更新时间）"""
        try:
            if not self.analysis_file_path.exists():
                return True

            analysis_mtime = self.analysis_file_path.stat().st_mtime

            # 检查向量数据库文件的修改时间
            vector_db_path = Path(self.config.vector_db_path)
            if vector_db_path.exists():
                for db_file in vector_db_path.rglob("*"):
                    if db_file.is_file() and db_file.stat().st_mtime > analysis_mtime:
                        return True

            return False

        except Exception as e:
            logger.error(f"检查分析结果是否过期失败: {e}")
            return True
