"""
LLM调试日志记录器
用于记录GDB执行、测试脚本执行、LLM API请求响应等详细信息
"""

import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from loguru import logger

def print_console(stream, prefix):
    """带超时的控制台输出打印"""
    import select

    try:
        while True:
            # 检查流是否关闭
            if stream.closed:
                break

            # 在Unix系统上使用select检查是否有数据可读
            if hasattr(select, 'select') and hasattr(stream, 'fileno'):
                try:
                    ready, _, _ = select.select([stream], [], [], 0.5)  # 0.5秒超时
                    if ready:
                        line = stream.readline()
                        if line:
                            logger.info(f"{prefix}: {line.rstrip()}")
                        else:
                            # EOF
                            break
                    # 如果没有数据可读，继续循环（避免CPU占用过高）
                except (ValueError, OSError):
                    # 流可能已关闭或无效
                    break
            else:
                # Windows系统或无法使用select，回退到阻塞读取
                try:
                    line = stream.readline()
                    if line:
                        logger.info(f"{prefix}: {line.rstrip()}")
                    else:
                        # EOF
                        break
                except (ValueError, OSError):
                    # 流可能已关闭
                    break

    except Exception as e:
        logger.debug(f"print_console异常: {e}")
    finally:
        logger.debug(f"{prefix} 输出线程结束")
        
class DebugLogger:
    """LLM调试日志记录器"""
    
    def __init__(self, config):
        self.config = config
        self.enabled = config.llm_debug
        self.debug_log_dir = Path("./debug")
        self.debug_log_dir.mkdir(exist_ok=True)
        
        # 创建调试日志文件
        if self.enabled:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.debug_log_file = self.debug_log_dir / f"llm_debug_{timestamp}.log"
            logger.info(f"LLM调试日志已启用，日志文件: {self.debug_log_file}")
    
    def _write_debug_log(self, category: str, data: Dict[str, Any]):
        """写入调试日志"""
        if not self.enabled:
            return
        
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "category": category,
                "data": data
            }
            
            with open(self.debug_log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False, indent=2) + "\n")
                f.write("-" * 80 + "\n")
                
        except Exception as e:
            logger.error(f"写入调试日志失败: {e}")
    
    def log_gdb_command(self, command: str, output: str, execution_time: float):
        """记录GDB命令执行"""
        if not self.enabled:
            return
        
        self._write_debug_log("gdb_command", {
            "command": command,
            "output": output,
            "execution_time_seconds": execution_time,
            "output_length": len(output)
        })
    
    def log_gdb_session_start(self, executable_path: str, args: List[str]):
        """记录GDB会话启动"""
        if not self.enabled:
            return
        
        self._write_debug_log("gdb_session_start", {
            "executable_path": executable_path,
            "arguments": args,
            "gdb_path": str(self.config.gdb_path)
        })
    
    def log_gdb_session_end(self, session_duration: float, exit_code: Optional[int]):
        """记录GDB会话结束"""
        if not self.enabled:
            return
        
        self._write_debug_log("gdb_session_end", {
            "session_duration_seconds": session_duration,
            "exit_code": exit_code
        })
    
    def log_test_script_execution(self, script_path: str, stdout: str, stderr: str, 
                                return_code: int, execution_time: float):
        """记录测试脚本执行"""
        if not self.enabled:
            return
        
        self._write_debug_log("test_script_execution", {
            "script_path": script_path,
            "stdout": stdout,
            "stderr": stderr,
            "return_code": return_code,
            "execution_time_seconds": execution_time,
            "stdout_length": len(stdout),
            "stderr_length": len(stderr)
        })
    
    def log_llm_request(self, provider: str, model: str, messages: List[Dict], 
                       tools: Optional[List[Dict]] = None):
        """记录LLM API请求"""
        #logger.info(f"[LLM] 请求消息: {messages} \n 工具列表: {tools}" )
        if not self.enabled:
            return
        
        # 计算请求的token数量（简单估算）
        total_chars = sum(len(str(msg.get("content", ""))) for msg in messages)
        estimated_tokens = total_chars // 4  # 粗略估算
        
        self._write_debug_log("llm_request", {
            "provider": provider,
            "model": model,
            "message_count": len(messages),
            "estimated_input_tokens": estimated_tokens,
            "has_tools": bool(tools),
            "tool_count": len(tools) if tools else 0,
            "messages": messages,  # 完整的消息内容
            "tools": tools if tools else []
        })
    
    def log_llm_response(self, llm_response):
        """记录LLM API响应"""
        #logger.info(f"[LLM] 响应: {llm_response.content}" )
        if not self.enabled:
            return
        
        self._write_debug_log("llm_response", {
            #"provider": provider,
            #"model": model,
            "response_content": llm_response.content,
            "response_length": len(llm_response.content),
            "response_time_seconds": llm_response.response_time,
            "usage": llm_response.usage or {},
            "tool_calls": llm_response.tool_calls,
        })
    
    def log_llm_error(self, provider: str, model: str, error: str, request_data: Dict):
        """记录LLM API错误"""
        if not self.enabled:
            return
        logger.info(f"[LLM] 请求失败: {error}" )
        
        self._write_debug_log("llm_error", {
            "provider": provider,
            "model": model,
            "error": error,
            "request_data": request_data
        })
    
    def log_debug_round_start(self, round_number: int, fault_description: str):
        """记录调试轮次开始"""
        if not self.enabled:
            return
        
        self._write_debug_log("debug_round_start", {
            "round_number": round_number,
            "fault_description": fault_description
        })
    
    def log_debug_round_end(self, round_number: int, result: Dict[str, Any], 
                           round_duration: float):
        """记录调试轮次结束"""
        if not self.enabled:
            return
        
        self._write_debug_log("debug_round_end", {
            "round_number": round_number,
            "result": result,
            "round_duration_seconds": round_duration
        })
    
    def log_program_output(self, output_type: str, content: str):
        """记录程序输出（stdout/stderr）"""
        if not self.enabled:
            return
        
        self._write_debug_log("program_output", {
            "output_type": output_type,  # "stdout" or "stderr"
            "content": content,
            "content_length": len(content)
        })
    
    def log_breakpoint_hit(self, breakpoint_info: Dict[str, Any], stack_frames: List[Dict]):
        """记录断点命中"""
        if not self.enabled:
            return
        
        self._write_debug_log("breakpoint_hit", {
            "breakpoint_info": breakpoint_info,
            "stack_frames": stack_frames,
            "stack_depth": len(stack_frames)
        })
    
    def log_variable_inspection(self, variables: List[Dict[str, Any]], scope: str):
        """记录变量检查"""
        if not self.enabled:
            return
        
        self._write_debug_log("variable_inspection", {
            "scope": scope,  # "local", "global", "args"
            "variables": variables,
            "variable_count": len(variables)
        })
    
    def get_debug_summary(self) -> Dict[str, Any]:
        """获取调试会话摘要"""
        if not self.enabled or not self.debug_log_file.exists():
            return {}
        
        try:
            # 统计各类日志条目
            categories = {}
            total_entries = 0
            
            with open(self.debug_log_file, "r", encoding="utf-8") as f:
                content = f.read()
                
            # 简单统计（实际实现可以更精确）
            for line in content.split("\n"):
                if '"category":' in line:
                    total_entries += 1
                    # 提取category
                    try:
                        category = line.split('"category": "')[1].split('"')[0]
                        categories[category] = categories.get(category, 0) + 1
                    except:
                        pass
            
            return {
                "debug_log_file": str(self.debug_log_file),
                "total_entries": total_entries,
                "categories": categories,
                "file_size_bytes": self.debug_log_file.stat().st_size
            }
            
        except Exception as e:
            logger.error(f"获取调试摘要失败: {e}")
            return {}
