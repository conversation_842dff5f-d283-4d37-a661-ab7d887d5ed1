
from typing import Dict, List, Any, Optional

class PromptManager:
    """提示词管理"""

    def __init__(self, config):
        self.config = config
        self.out_format = """
            当无需工具调用时，请严格按照以下JSON格式输出:
             ```json
            {
                "error_found": "是否确定故障原因：true/false",
                "explanation": "详细解释故障原因,指出具体的代码错误",
                "recommendation": "建议的修复方案，给出具体的修复代码",
                "next_step": "下一步操作：重新调试/结束调试/单步调试/检索代码/继续分析/无"
            }
            ```
            """
    def create_system_prompt(self, project_analysis) -> str:
        """创建系统提示"""
        prompt_parts = [
            "你是一个专业的C/C++调试专家，精通GDB、内存分析、并发调试等各种调试技术，目标是通过gdb调试指定的C/C++项目，准确定位异常原因。",
            "",
            "## 核心调试原则",
            "1. **系统性分析**: 从程序状态、调用栈、内存布局等多维度分析问题",
            "2. **假设驱动**: 基于症状提出假设，然后通过调试验证或排除",
            "3. **最小化干扰**: 优先使用非侵入性的调试方法",
            "4. **渐进式深入**: 从高层次概览到具体细节逐步深入",
            "5. **上下文感知**: 充分利用前几轮调试的发现和经验",
            "",
            "## 调试策略",
            "- **异常分析**: 当遇到SIGSEGV、SIGABRT等信号时，重点分析崩溃点的内存状态和调用路径，结合代码分析原因",
            "- **断点策略**: 在关键函数入口、循环边界、内存操作前后设置断点",
            "- **变量追踪**: 重点关注指针、数组索引、内存分配相关变量的变化",
            "- **模式识别**: 识别常见错误模式（空指针、缓冲区溢出、悬挂指针、内存泄漏等）",
            "",
            "## 调试流程",
            "1.当故障复现或到断点时,分析用户提供的gdb上下文（堆栈帧、内存变量值、相关代码等），结合代码分析故障原因。",
            "2.如果已经100%确定故障的原因，则给出具体的分析，明确到具体的文件和行号，终止调试。",
            "3.如果还不能确定具体的故障原因，则可能需要工具调用，比如增删断点、查看线程、查看堆栈、单步调试等。",
            "4.当一轮调试不能确定故障原因时，进行多轮调试，直到100%确定故障原因。",
            "5.新一轮调试时，需要分析历史调试记录的总结，并确定本轮调试的策略，比如：设置新的断点等",
            "6.**重要**：如果认为已有断点对故障分析帮助不大，可以取消该断点",
            f"""## 待调试项目的分析:
            {project_analysis}
            """,
            "## 工具使用",
            "你可以根据用户提供的上下文选择合适的工具来进行调试分析。",
            f"## 输出格式:\n{self.out_format}"
            ]
        
        # 项目结构信息
        # if project_analysis.get("main_files"):
        #     prompt_parts.append("主要文件:")
        #     for main_file in project_analysis["main_files"]:
        #         prompt_parts.append(f"  - {main_file['file']}:{main_file['line']}")

        # if project_analysis.get("source_files"):
        #     prompt_parts.append(f"源文件数量: {len(project_analysis['source_files'])}")
        #     prompt_parts.append(f"头文件数量: {len(project_analysis.get('header_files', []))}")


        # # 添加可用工具列表
        # if context.get("available_tools"):
        #     prompt_parts.extend([
        #         "你可以使用以下工具来协助调试：",
        #         ""
        #     ])

        #     tools = context["available_tools"]
        #     for i, tool in enumerate(tools, 1):
        #         if isinstance(tool, dict):
        #             tool_name = tool.get("name", "未知工具")
        #             tool_desc = tool.get("description", "无描述")
        #             prompt_parts.append(f"{i}. {tool_name}: {tool_desc}")
        #         else:
        #             # 如果是Tool对象
        #             prompt_parts.append(f"{i}. {tool.name}: {tool.description}")

        #     prompt_parts.extend([
        #         "",
        #         "请根据当前情况选择合适的工具来进行调试分析。",
        #         "在回复中明确说明你要使用哪些工具以及使用的原因。",
        #         ""
        #     ])

        # prompt_parts.append("请根据提供的信息进行分析，并给出具体的调试步骤。")
        
        # 添加上下文信息
        # if context.get("project_info"):
        #     prompt_parts.extend([
        #         "",
        #         f"项目信息: {context['project_info']}"
        #     ])
        
        # if context.get("error_description"):
        #     prompt_parts.extend([
        #         "",
        #         f"初始的错误描述: {context['error_description']}"
        #     ])
                
        return "\n".join(prompt_parts)
    def create_initial_analysis_prompt(self, fault_description: str, round_summary: List[str]) -> str:
        """创建初始分析提示(user)"""
        prompt_parts = [
            "请根据初始的错误描述及历史调试记录总结，对本次调试的C/C++项目进行综合分析，并调用工具分析代码，找出关键位置设置断点，断点要求有明确的文件名及行号，如果不能确认关键位置可以不用设置断点。\n",
            "",
            f"## 初始的错误描述: {fault_description}\n",
            ""
        ]
        if len(round_summary) > 0:
            history_lines = "\\n".join([f"第{i+1}轮: {summary}" for i, summary in enumerate(round_summary)])
            history = f"""## 历史调试记录总结：
    {history_lines}
    """
        else:
            history = "## 历史调试记录总结：无"
        prompt_parts.append(history)

        return "\n".join(prompt_parts)
    def create_debug_state_prompt(
        self, fault_description: str, debug_state, context_info: Dict[str, Any], round_result: Dict[str, Any]
    ) -> str:
        """创建行动决策提示"""
        prompt_parts = [
            f"程序已停止运行，需要决定下一步行动：",
            f"",
            f"初始故障描述: {fault_description}",
            f"",
            f"程序当前状态：\n{context_info}"
        ]

        prompt_parts.extend([
            f"",
            f"请决定下一步最佳行动：",
            f"1. 如果当前位置可能包含错误信息，选择合适的调试工具",
            f"2. 如果当前位置无关紧要，可以继续执行或设置新断点",
            f"3. 如果判断当前断点无法找到错误，建议重新运行程序货继续执行到下一个断点",
            f"4. 如果已经触发异常信号，可检查代码是否发现问题",
            f"5. 如果认为本轮调试已经找到明确的错误原因，可终止调试",
            f"",
            f"请使用工具调用来执行你的决策。"
        ])

        return "\n".join(prompt_parts)
    def create_state_analysis_prompt(self, debug_state) -> str:
        """创建状态分析提示"""
        prompt_parts = [
            "请分析当前的调试状态：",
            ""
        ]
        
        if debug_state:
            prompt_parts.extend([
                f"程序状态: {'运行中' if debug_state.is_running else '已停止'}",
                f"停止原因: {debug_state.stop_reason}",
            ])
            
            if debug_state.current_frame:
                frame = debug_state.current_frame
                prompt_parts.extend([
                    f"当前函数: {frame.function}",
                    f"文件位置: {frame.file}:{frame.line}",
                ])
            
            if debug_state.variables:
                prompt_parts.append("局部变量:")
                for name, var in list(debug_state.variables.items())[:5]:  # 限制显示数量
                    prompt_parts.append(f"  {name} = {var.value}")
        
        prompt_parts.extend([
            "",
            "请回答以下问题：",
            "1. 当前状态是否表明存在问题？",
            "2. 如果存在问题，可能的原因是什么？",
            "3. 问题是否已经解决？",
            "4. 下一步应该采取什么行动？"
        ])
        
        return "\n".join(prompt_parts)
    
    def create_action_planning_prompt(self, debug_state, analysis_result: Dict[str, Any]) -> str:
        """创建行动规划提示"""
        prompt_parts = [
            "基于当前分析结果，请规划下一步的调试行动：",
            "",
            f"分析结果: {analysis_result.get('analysis', '无')}",
            "",
            "可用的调试工具包括：",
            "- set_breakpoint: 设置断点",
            "- step_over: 单步执行（跳过函数）",
            "- step_into: 单步执行（进入函数）",
            "- continue_execution: 继续执行",
            "- get_variables: 获取变量信息",
            "- evaluate_expression: 计算表达式",
            "- search_code: 搜索相关代码",
            "",
            "请选择最合适的工具并说明理由。"
        ]
        
        return "\n".join(prompt_parts)
    def _create_exception_analysis_system_prompt(self) -> str:
        """创建异常分析的系统提示"""
        return """你是一个资深的系统级调试专家，专门处理C/C++程序的异常信号分析。

## 异常信号分析框架

### SIGSEGV (段错误) 分析要点：
1. **内存访问模式**: 检查是否访问了无效内存地址（NULL、野指针、已释放内存）
2. **栈溢出检测**: 分析调用栈深度和局部变量大小
3. **数组边界**: 检查数组/缓冲区访问是否越界
4. **指针运算**: 验证指针算术操作的正确性

### SIGABRT (异常终止) 分析要点：
1. **断言失败**: 检查assert()调用和条件
2. **内存管理错误**: double free、free(NULL)等
3. **标准库错误**: malloc失败、文件操作错误等

### 分析方法论：
1. **崩溃点分析**: 精确定位异常发生的代码行和上下文
2. **数据流追踪**: 追踪导致异常的变量从定义到使用的完整路径
3. **内存布局检查**: 分析相关内存区域的状态和内容
4. **时序分析**: 考虑多线程环境下的竞态条件

请基于这个框架进行深入分析，提供准确的根因定位和解决方案。"""

    def _create_exception_analysis_prompt(
        self, fault_description: str, debug_state, context_info: Dict[str, Any]
    ) -> str:
        """创建异常分析提示"""
        prompt_parts = [
            f"程序发生异常，需要分析错误原因：",
            f"",
            f"故障描述: {fault_description}",
            f"停止原因: {debug_state.stop_reason}",
            f"",
            f"调用栈信息:"
        ]

        # 添加调用栈信息
        stack_frames = context_info.get("stack_frames", [])
        for i, frame in enumerate(stack_frames[:5]):  # 只显示前5层
            prompt_parts.append(f"  #{i} {frame.function} at {frame.file}:{frame.line}")

        # 添加局部变量信息
        local_vars = context_info.get("local_variables", {})
        if local_vars:
            prompt_parts.append(f"")
            prompt_parts.append(f"局部变量:")
            for name, var in list(local_vars.items())[:10]:  # 只显示前10个变量
                prompt_parts.append(f"  {name} = {var.value}")

        prompt_parts.extend([
            f"",
            f"请分析：",
            f"1. 这个异常的具体原因是什么？",
            f"2. 是否已经找到了错误的根本原因？",
            f"3. 如果找到了，请提供具体的解决方案",
            f"4. 如果没找到，还需要进一步调试什么？"
        ])

        return "\n".join(prompt_parts)

    def _create_action_decision_system_prompt(self) -> str:
        """创建行动决策的系统提示"""
        return """你是一个智能调试决策引擎，具备高级的调试策略规划能力。

## 决策框架

### 1. 上下文评估
- **位置重要性**: 评估当前断点位置对错误定位的价值
- **数据可见性**: 分析当前作用域内可观察的关键变量
- **执行路径**: 考虑程序执行到此处的路径是否异常
- **历史信息**: 利用前几轮调试的发现和经验

### 2. 行动优先级
**高优先级行动**:
- 检查可疑指针和数组的值与边界
- 在内存操作前后设置观察点
- 分析函数参数的有效性

**中优先级行动**:
- 单步执行观察变量变化
- 检查函数返回值
- 分析循环条件和计数器

**低优先级行动**:
- 查看无关变量
- 在远离错误的位置设置断点

### 3. 终止条件判断
**建议重新运行的情况**:
- 当前位置明显与错误无关（如初始化代码、工具函数）
- 已经深入调试但未发现有价值信息
- 需要从不同的执行路径开始分析

### 4. 工具选择策略
- **evaluate_expression**: 优先检查可疑变量
- **step_over/step_into**: 用于观察关键操作的执行
- **set_breakpoint**: 在关键函数或内存操作处设置
- **get_memory_info**: 检查指针指向的内存内容

请基于这个框架做出最优的调试决策。"""
    def _create_round_summary_system_prompt(self) -> str:
            """创建轮次总结的系统提示"""
            return """你是一个专业的调试总结专家。你需要对每轮调试的结果进行总结，包括：

    1. 本轮调试的主要发现和进展
    2. 执行的关键调试步骤
    3. 遇到的问题和障碍
    4. 对下一轮调试的建议
    5. 错误定位的进展情况

    请提供简洁但全面的总结，重点关注对后续调试有价值的信息。"""

    def _create_round_summary_prompt(
        self, round_result: Dict[str, Any], fault_description: str
    ) -> str:
        """创建轮次总结提示"""
        prompt_parts = [
            f"请总结第 {round_result['round']} 轮调试的结果：",
            f"",
            f"故障描述: {fault_description}",
            f"轮次状态: {'成功' if round_result.get('success', False) else '失败'}",
            f"终止原因: {round_result.get('termination_reason', '未知')}",
            f"执行步数: {round_result.get('step_count', 0)}",
            f"",
        ]

        # 添加错误发现信息
        if round_result.get("error_found"):
            prompt_parts.append(f"✓ 找到错误原因: {round_result.get('error_cause', '未详细说明')}")
            if round_result.get("solution"):
                prompt_parts.append(f"✓ 解决方案: {round_result.get('solution')}")
        elif round_result.get("error_reproduced"):
            prompt_parts.append(f"✓ 成功复现错误，但未找到根本原因")
        else:
            prompt_parts.append(f"✗ 未能复现或定位错误")

        prompt_parts.append(f"")

        # 添加调试步骤信息
        debug_steps = round_result.get("debug_steps", [])
        if debug_steps:
            prompt_parts.append(f"主要调试步骤:")
            for i, step in enumerate(debug_steps[-5:], 1):  # 只显示最后5步
                step_desc = step.get("step", "未知步骤")
                prompt_parts.append(f"  {i}. {step_desc}")

        prompt_parts.extend([
            f"",
            f"请提供总结，包括：",
            f"1. 本轮的主要发现",
            f"2. 关键的调试行动",
            f"3. 遇到的问题",
            f"4. 对下轮调试的建议"
        ])

        return "\n".join(prompt_parts)


    def _create_error_analysis_prompt(
        self, fault_description: str, debug_state, analysis_result: Dict,
        memory_analysis: Dict, stack_analysis: Dict
    ) -> str:
        """创建错误分析提示"""
        prompt_parts = [
            "请分析以下C/C++程序的错误信息，并判断错误的根本原因：",
            "",
            f"错误描述: {fault_description}",
            ""
        ]

        # 添加程序状态信息
        if debug_state:
            prompt_parts.extend([
                "程序当前状态:",
                f"- 程序状态: {'运行中' if debug_state.is_running else '已停止'}",
                f"- 停止原因: {debug_state.stop_reason}",
            ])

            if debug_state.current_frame:
                frame = debug_state.current_frame
                prompt_parts.extend([
                    f"- 当前函数: {frame.function}",
                    f"- 文件位置: {frame.file}:{frame.line}",
                ])

        # 添加内存分析信息
        if memory_analysis.get("memory_issues"):
            prompt_parts.extend([
                "",
                "发现的内存问题:",
            ])
            for issue in memory_analysis["memory_issues"]:
                prompt_parts.append(f"- {issue['type']}: {issue['description']}")

        # 添加调用栈信息
        if stack_analysis.get("frames"):
            prompt_parts.extend([
                "",
                "调用栈信息:",
            ])
            for frame in stack_analysis["frames"][:5]:  # 显示前5层
                prompt_parts.append(f"- #{frame['level']}: {frame['function']} ({frame['file']}:{frame['line']})")

        if stack_analysis.get("potential_issues"):
            prompt_parts.extend([
                "",
                "调用栈中的潜在问题:",
            ])
            for issue in stack_analysis["potential_issues"]:
                prompt_parts.append(f"- {issue['description']}")

        prompt_parts.extend([
            "",
            "请回答以下问题：",
            "1. 根据以上信息，错误的根本原因是什么？",
            "2. 这个错误是否已经可以确定原因？(回答 true 或 false)",
            "3. 如果可以确定，请提供具体的解决方案。",
            "4. 如果不能确定，还需要检查哪些方面？",
            "",
            "请以结构化的方式回答，明确标明是否找到了错误原因。"
        ])

        return "\n".join(prompt_parts)