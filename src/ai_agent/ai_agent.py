"""
AI Agent主模块
"""

import asyncio
import uuid
import time
import threading
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger

from .llm_client import LLMClient, Message, LLMResponse
from .tool_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>all, ToolR<PERSON>ult
from .memory_manager import MemoryManager, DebuggingSession
from .prompt import PromptManager
from ..utils.debug_logger import Debug<PERSON>ogger,print_console
from ..gdb_controller.gdb_controller import GDBController
from ..knowledge_base.code_filter import create_code_filter


class AIAgent:
    """AI调试代理"""

    def __init__(self, config, knowledge_base, gdb_controller: GDBController):
        self.config = config
        self.knowledge_base = knowledge_base
        self.gdb_controller = gdb_controller
        self.bp_ids = []

        # 初始化组件
        self.tool_manager = ToolManager(gdb_controller, knowledge_base)
        self.llm_client = LLMClient(config, self.tool_manager)
        self.debug_logger = DebugLogger(config)
        self.memory_manager = MemoryManager(config)
        self.prompt_manager = PromptManager(config)

        # 状态管理
        self.current_session: Optional[DebuggingSession] = None
        self.is_debugging = False
        self.max_debug_rounds = config.max_debug_rounds
        self.current_round = 0

        # 事件广播回调
        self.event_broadcaster = None

    def set_event_broadcaster(self, broadcaster):
        """设置事件广播器"""
        self.event_broadcaster = broadcaster

    async def _broadcast_debug_output(self, message: str, output_type: str = "info"):
        """广播调试输出"""
        if output_type == "debug":
            logger.info(message)
        elif output_type == "info":
            logger.info(message)
        elif output_type == "error":
            logger.error(message)
        elif output_type == "warning":
            logger.warning(message)
        if self.event_broadcaster:
            await self.event_broadcaster({
                "type": "debug_output",
                "output": message,
                "output_type": output_type,
                "round": self.current_round,
                "timestamp": asyncio.get_event_loop().time()
            })
    
    async def start_debug_session(self, fault_description: str) -> Dict[str, Any]:
        """开始调试会话（立即返回，后台执行）"""
        try:
            logger.info(f"开始AI调试会话: {fault_description}")

            # 检查是否已有调试会话在运行
            if self.is_debugging:
                return {"success": False, "error": "调试会话已在进行中"}

            # 生成会话ID
            session_id = str(uuid.uuid4())[:8]

            # 设置调试状态
            self.is_debugging = True
            self.current_round = 0

            # 在后台启动调试会话
            asyncio.create_task(self._run_debug_session_background(fault_description))

            return {"success": True, "message": "调试会话已启动", "session_id": session_id}

        except Exception as e:
            logger.error(f"启动调试会话失败: {e}")
            self.is_debugging = False
            if self.current_session:
                self.memory_manager.end_session(self.current_session.session_id, "failed", str(e))
            return {"success": False, "error": str(e)}

    async def _run_debug_session_background(self, fault_description: str):
        """在后台运行调试会话"""
        try:
            logger.info(f"后台调试会话开始: {fault_description}")

            # 加载项目分析内容
            self.project_analysis = await self._load_project_analysis()

            # 启动GDB会话
            if not await self.gdb_controller.start_debug_session():
                logger.error("GDB会话启动失败")
                self.is_debugging = False
                return
            
            self.is_debugging = True
            self.current_round = 0

            while self.is_debugging and self.current_round < self.max_debug_rounds:
                self.current_round += 1
                await self._broadcast_debug_output(f"开始调试轮次 {self.current_round}/{self.max_debug_rounds}", "info")
                try:
                    # 开始调试循环
                    result = await self._execute_debug_round(fault_description)
                    
                    logger.info(f"---第{self.current_round}轮, 调试结果: {result}")
                    if result["error_found"]:
                        break
                
                except Exception as e:
                    logger.error(f"调试轮次 {self.current_round} 失败: {e}")
                    self.memory_manager.add_observation_memory(
                        f"调试轮次失败: {e}",
                        {"round": self.current_round, "error": str(e)}
                    )

            if self.current_round >= self.max_debug_rounds:
                logger.warning("达到最大调试轮次，未能找到错误原因")
                return {
                    "success": False,
                    "error": "达到最大调试轮次",
                    "summary": f"经过{self.current_round}轮次调试，未能找到错误根本原因",
                    "rounds": self.current_round
                }

            logger.info(f"调试会话完成: {result}")

        except BaseException as e:
            logger.error(f"后台调试会话失败: {e}")

        finally:
            # 清理资源
            await self.gdb_controller.stop_debug_session()
            self.current_session = None
            self.is_debugging = False
            self.current_round = 0      

    async def _execute_debug_round(self, fault_description: str) -> Dict[str, Any]:
        """执行一轮完整的调试流程"""
        logger.info(f"执行第{self.current_round}轮调试")
        
        round_result = {
            "round": self.current_round,
            "error_reproduced": False,
            "error_found": False,
            "explanation": "",
            "recommendation": "",
            "next_step": "continue",
            "step_count": 0,
            "termination_reason": None #本轮调试终止原因
        }
        stop_keywords = ["restart", "termination"]

        try:
            # 1. 总结之前的调试结果
            last_summary = await self.llm_client.build_summary()
            system_prompt = self.prompt_manager.create_system_prompt(self.project_analysis)
            # 开启新的llm会话
            self.llm_client.start_new_session(system_prompt)

            # 2. 分析代码并设置初始断点
            await self._broadcast_debug_output("步骤2: 分析代码并设置断点", "info")
            round_result = await self._initial_analysis(fault_description, round_result, last_summary)
            if round_result["error_found"]:
                # 代码分析找到故障原因
                return round_result

            round_result["next_step"] = "continue"
            # 4. 运行程序
            await self._broadcast_debug_output("步骤3: 运行程序", "info")
            args = self.config.target_args.split() if self.config.target_args else []
            success = await self.gdb_controller.run_program(args)
            if not success:
                round_result["explanation"] = "程序启动失败"
                return round_result
            logger.info("程序已启动")

            # 4. 如果有测试脚本，启动测试脚本
            test_process = None

            # 5. 进入调试监控循环
            await self._broadcast_debug_output("步骤5: 进入调试监控循环", "debug")
            while True:
                await asyncio.sleep(1)  # 短暂等待

                # 检查测试脚本是否完成（终止条件3.2）
                if test_process and test_process.returncode is not None:
                    await self._broadcast_debug_output("测试脚本执行完毕", "info")
                    round_result["termination_reason"] = "test_script_completed"
                    round_result["explanation"] = "测试脚本执行完毕，程序正常退出"
                    round_result["next_step"] = "restart"
                    break

                # 获取当前调试状态
                debug_state = await self.gdb_controller.get_debug_state()
                if not debug_state:
                    continue
                
                # 检查程序是否仍在运行
                if not debug_state.is_stopped:
                    # 在运行状态，启动测试脚本
                    if test_process is None and self.config.test_script_path:
                        script_result = await self._execute_test_script()
                        if script_result.get("success"):
                            test_process = script_result.get("process")
                    continue

                # 程序停止了，分析停止原因
                await self._broadcast_debug_output(f"程序停止，原因: {debug_state.stop_reason}", "debug")

                # 检查是否是异常信号（终止条件3.1）
                if self._is_exception_signal(debug_state.stop_reason):
                    await self._broadcast_debug_output("检测到异常信号", "warning")
                    round_result["error_reproduced"] = True
                    round_result["termination_reason"] = "检测到异常信号"
                    round_result["next_step"] = "restart"

                    # 分析异常并让LLM判断
                    round_result = await self._llm_analysis_debug_state(
                        fault_description, debug_state, round_result
                    )
                    round_result["explanation"] = f"复现异常, {round_result['explanation']}"

                # 如果是断点停止，进行智能调试
                elif "breakpoint" in debug_state.stop_reason.lower():
                    await self._broadcast_debug_output("命中断点，进行智能分析", "info")

                    round_result["next_step"] = "continue"
                    # 让LLM分析当前状态并决定下一步行动
                    round_result = await self._llm_analysis_debug_state(
                        fault_description, debug_state, round_result
                    )
                    # 让LLM分析该断点是否有帮助
                    round_result = await self._llm_plan_action("根据已有的分析，判断当前程序停止处的断点是否对故障分析有判断，如果没有帮助则可以取消该断点", round_result)

                else:
                    # 其他停止原因
                    await self._broadcast_debug_output(f"程序因其他原因停止: {debug_state.stop_reason}", "info")
                    round_result["termination_reason"] = "other_stop_reason"
                    round_result["explanation"] = f"程序停止: {debug_state.stop_reason}"
                    round_result["next_step"] = "restart"

                # 根据配置决定终止条件
                if self.config.exception_only_termination:
                    # 只有异常信号才终止
                    if self._is_exception_signal(debug_state.stop_reason):
                        break
                else:
                    # 原有逻辑：检查next_step中的关键词
                    if any(keyword in round_result.get("next_step") for keyword in stop_keywords):
                        break

        except Exception as e:
            logger.error(f"调试轮次执行失败: {e}")
            round_result["explanation"] = f"调试轮次执行失败: {e}"
            round_result["next_step"] = "restart"

        finally:
            # 清理断点
            await self._clean_breakpoints()
            # 停止程序
            await self.gdb_controller.stop_program()

        return round_result
    
    def _is_exception_signal(self, stop_reason: str) -> bool:
        """检查是否是异常信号"""
        exception_signals = ["SIGSEGV", "SIGABRT", "SIGFPE", "SIGBUS", "SIGILL"]
        return any(signal in stop_reason.upper() for signal in exception_signals)

    async def _llm_analysis_debug_state(
        self, fault_description: str, debug_state, round_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """让LLM决定下一步行动"""
        # 收集上下文信息
        context_info = await self.gdb_controller.collect_debug_context(debug_state)
        error = context_info.get("error")
        if error:
            round_result["explanation"] = f"收集上下文信息失败：{error}"
        else:
            # 构建决策提示
            content=self.prompt_manager.create_debug_state_prompt(
                    fault_description, debug_state, context_info, round_result
            )
            round_result = await self._llm_plan_action(content, round_result)
        
        return round_result
    
    async def _llm_plan_action(self, content, round_result: Dict[str, Any]) -> Dict[str, Any]:
        """让LLM分析并决定下一步行动"""
        try:
            # 添加工具定义
            tools = self.tool_manager.get_tools_schema()
            while True:
                # 获取LLM决策，执行工具调用
                last_step = round_result["next_step"]
                llm_response = await self.llm_client.session_completion(content, tools=tools)
                
                content = "" # 用户请求清理，避免再次发送
                if len(llm_response.tool_calls) == 0:
                    try:
                        json_start = llm_response.content.find('{')
                        json_end = llm_response.content.rfind('}') + 1
                        result = json.loads(llm_response.content[json_start:json_end])
                        next_step = self._parse_next_step(result["next_step"], "analysis")
                        round_result["error_found"] = result["error_found"]
                        round_result["explanation"] = result["explanation"]
                        round_result["recommendation"] = result["recommendation"]
                        round_result["next_step"] = next_step
                        if next_step == "analysis":
                            content = llm_response.content
                            if last_step == "analysis":
                                break
                            else:
                                continue
                    except Exception as e:
                        content = f"大模型输出格式错误，{self.prompt_manager.out_format}"
                        #content = llm_response.content
                        continue
                        #round_result["explanation"] = llm_response.content
                    break
                
                is_step_tool = False
                for tool_call_data in llm_response.tool_calls:
                    result = await self.llm_client.call_tool(tool_call_data)
                    if self.tool_manager.is_step_tool(tool_call_data):
                        is_step_tool = True
                if is_step_tool:
                    break

        except Exception as e:
            logger.error(f"LLM行动决策失败: {e}")
            round_result["next_step"] = "restart"
            round_result["explanation"] = f"执行工具调用失败：{str(e)}"
        
        return round_result
    def _parse_next_step(self, next_step: str, default_step) -> str:
        """解析LLM建议的下一步动作"""
        restart_keywords = ["重新运行", "继续运行", "重启程序", "restart", "重新开始"]
        if any(keyword in next_step for keyword in restart_keywords):
            return "restart"
        keywords = ["停止", "结束", "终止"]
        if any(keyword in next_step for keyword in keywords):
            return "termination"
        keywords = ["继续分析"]
        if any(keyword in next_step for keyword in keywords):
            return "analysis"
        return default_step

    async def _restart_gdb_session(self) -> Dict[str, Any]:
        """重启目标服务进程"""
        try:
            logger.info("重启目标服务进程")

            # 停止当前GDB会话
            await self.gdb_controller.stop_debug_session()

            # 短暂等待确保进程完全停止
            await asyncio.sleep(1.0)

            # 重新启动GDB会话
            if not await self.gdb_controller.start_debug_session():
                logger.error("GDB会话重启失败")
                return {"success": False, "error": "GDB会话重启失败"}

            logger.info("目标服务进程重启成功")
            return {"success": True, "message": "目标服务进程重启成功"}

        except Exception as e:
            logger.error(f"重启目标服务进程失败: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_test_script(self) -> Dict[str, Any]:
        """执行测试脚本"""
        try:
            if not self.config.test_script_path:
                return {"success": False, "error": "未配置测试脚本路径"}

            script_path = Path(self.config.test_script_path)
            if not script_path.exists():
                return {"success": False, "error": f"测试脚本不存在: {script_path}"}
            
            await self._broadcast_debug_output(f"执行测试脚本: {script_path}", "info")

            # 根据脚本类型执行
            if script_path.suffix == '.py':
                # Python脚本
                test_process = await asyncio.create_subprocess_exec(
                    'python', str(script_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.config.project_code_dir
                )
            elif script_path.suffix == '.sh':
                # Shell脚本
                test_process = await asyncio.create_subprocess_exec(
                    'bash', str(script_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.config.project_code_dir
                )
            else:
                # 直接执行
                test_process = await asyncio.create_subprocess_exec(
                    str(script_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=self.config.project_code_dir
                )
            stdout_thread = threading.Thread(
                target=print_console,
                args=(test_process.stdout, "TEST-STDOUT"),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=print_console,
                args=(test_process.stderr, "TEST-STDERR"),
                daemon=True
            )
            stdout_thread.start()
            stderr_thread.start()
            
            await self._broadcast_debug_output(f"执行测试脚本成功!", "info")
            return {"success": True, "process": test_process}

        except Exception as e:
            await self._broadcast_debug_output(f"执行测试脚本失败: {e}", "error")
            return {"success": False, "error": str(e)}
    
    async def _initial_analysis(self, fault_description: str, round_result: Dict[str, Any], last_summary:List[str] = []) -> Dict[str, Any]:
        """初始分析 - 第一阶段：根据错误上下文和历史调试记录，分析可以设置的断点"""
        logger.info("开始第一阶段分析：设置初始断点")
        
        # 创建分析提示
        analysis_prompt = self.prompt_manager.create_initial_analysis_prompt(
                fault_description, last_summary
        )
        round_result = await self._llm_plan_action(analysis_prompt,round_result)
        return round_result

        # # 1. 项目结构分析
        # project_analysis = await self._analyze_project_structure()

        # # 2. 错误相关代码搜索
        # history_summary = "\n".join(round_summary)
        # search_results = self.knowledge_base.search_code(fault_description + history_summary, top_k=10)

        # # 3. 符号和函数分析
        # symbol_analysis = await self._analyze_relevant_symbols(fault_description, search_results)

        # # 4. 获取调试建议
        # suggestions = self.knowledge_base.get_debugging_suggestions(fault_description)

        # # 5. 使用LLM进行综合分析
        # comprehensive_analysis = await self._llm_comprehensive_analysis(
        #     fault_description, project_analysis, search_results, symbol_analysis, suggestions, history_summary
        # )

        # # 6. 记录分析结果
        # self.memory_manager.add_observation_memory(
        #     f"第一阶段分析完成：项目结构分析、代码搜索({len(search_results)}个结果)、符号分析、LLM综合分析",
        #     {
        #         "project_analysis": project_analysis,
        #         "search_results_count": len(search_results),
        #         "symbol_analysis": symbol_analysis,
        #         "suggestions_count": len(suggestions),
        #         "comprehensive_analysis": comprehensive_analysis,
        #         "fault_description": fault_description
        #     }
        # )

        # # 7. 记录初始断点
        # self.initial_breakpoints = comprehensive_analysis.get("suggested_breakpoints", [])
        
        # # 设置断点
        # await self._set_breakpoints(self.initial_breakpoints)
        
        # context = {
        #         "project_analysis": project_analysis,
        #         "symbol_analysis": symbol_analysis,
        #         "available_tools": self.tool_manager.get_all_tools()
        # }

        # logger.info("第一阶段分析完成")
        
        # return context

    async def _analyze_project_structure(self) -> Dict[str, Any]:
        """分析项目结构"""
        try:
            # 获取项目基本信息
            project_info = {
                "project_dir": str(self.config.project_code_dir),
                "target_executable": str(self.config.target_executable) if self.config.target_executable else None,
                "target_args": self.config.target_args,
                "main_files": [],
                "header_files": [],
                "source_files": []
            }
            code_filter = create_code_filter(self.config.code_filter, include_defaults=True)

            # 搜索主要文件
            main_symbols = self.knowledge_base.find_symbol("main")
            if main_symbols:
                project_info["main_files"] = [
                    {"file": symbol.file_path, "line": symbol.line_number}
                    for symbol in main_symbols[:3]
                ]

            # 获取文件统计信息
            all_files = self.knowledge_base.get_all_files()
            if all_files:
                project_info["source_files"] = [f for f in all_files if f.endswith(('.c', '.cpp', '.cc'))][:10]
                project_info["header_files"] = [f for f in all_files if f.endswith(('.h', '.hpp'))][:10]

            return project_info

        except Exception as e:
            logger.error(f"项目结构分析失败: {e}")
            return {"error": str(e)}

    async def _load_project_analysis(self):
        """加载项目分析内容"""
        try:
            logger.info("加载项目分析内容...")

            # 首先尝试使用LLM进行完整的项目分析
            #project_analysis = await self.knowledge_base.analyze_project_with_llm()
            project_analysis = self.knowledge_base.load_analysis()

            if project_analysis:
                logger.info("成功加载完整的项目分析内容")
                return project_analysis["llm_analysis"]

            # 如果都失败，回退到基础的项目结构分析
            logger.warning("无法加载项目分析，回退到基础分析")
            return await self._analyze_project_structure()

        except Exception as e:
            logger.error(f"加载项目分析失败: {e}")
            # 回退到基础分析
            try:
                return await self._analyze_project_structure()
            except Exception as fallback_error:
                logger.error(f"基础项目分析也失败: {fallback_error}")
                return {"error": str(e), "fallback_error": str(fallback_error)}

    async def _analyze_relevant_symbols(self, fault_description: str, search_results) -> Dict[str, Any]:
        """分析相关符号"""
        try:
            symbol_analysis = {
                "relevant_functions": [],
                "relevant_variables": [],
                "potential_issues": []
            }

            # 从搜索结果中提取符号
            for result in search_results[:5]:
                if hasattr(result, 'chunk') and hasattr(result.chunk, 'symbols'):
                    for symbol in result.chunk.symbols:
                        if symbol.type == "function":
                            symbol_analysis["relevant_functions"].append({
                                "name": symbol.name,
                                "file": symbol.file_path,
                                "line": symbol.line_number
                            })
                        elif symbol.type in ["variable", "parameter"]:
                            symbol_analysis["relevant_variables"].append({
                                "name": symbol.name,
                                "file": symbol.file_path,
                                "line": symbol.line_number
                            })

            # 基于错误描述查找特定符号
            error_keywords = ["malloc", "free", "null", "pointer", "segfault", "crash"]
            for keyword in error_keywords:
                if keyword.lower() in fault_description.lower():
                    symbols = self.knowledge_base.find_symbol(keyword)
                    if symbols:
                        symbol_analysis["potential_issues"].append({
                            "keyword": keyword,
                            "symbols": [{"name": s.name, "file": s.file_path, "line": s.line_number} for s in symbols[:3]]
                        })

            return symbol_analysis

        except Exception as e:
            logger.error(f"符号分析失败: {e}")
            return {"error": str(e)}
        
    async def _clean_breakpoints(self):
        """取消所有断点"""
        if self.bp_ids:
            for bp_id in self.bp_ids:
                await self.gdb_controller.remove_breakpoint(bp_id)
    
