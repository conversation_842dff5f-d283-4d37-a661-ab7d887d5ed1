"""
LLM客户端
"""

import json
import random
import re
import os
import time
from typing import List, Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass
from loguru import logger

from ..utils.debug_logger import DebugLogger
from .mock_client import MockClient
from .tool_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>all, ToolResult

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI库不可用")


@dataclass
class Message:
    """消息"""
    role: str  # system, user, assistant
    content: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMResponse:
    """LLM响应"""
    content: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    tool_calls: Optional[List[Any]] = None
    response_time: float = 0


class LLMClient:
    """LLM客户端"""

    def __init__(self, config, tool_manager:ToolManager):
        self.config = config
        self.tool_manager = tool_manager
        self.debug_logger = DebugLogger(config)
        self.messages = []
        self.round_summary = []
        self.event_broadcaster = None  # 事件广播器
        try:
            if self.config.llm_provider == "openai":
                self._init_openai_client()
            elif self.config.llm_provider == "mock":
                self._init_mock_client()
            else:
                raise ValueError(f"不支持的LLM提供商: {self.config.llm_provider}")
        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
            raise
    
    def _init_openai_client(self):
        """初始化OpenAI客户端"""
        if not OPENAI_AVAILABLE:
            raise RuntimeError("OpenAI库不可用")
        
        client_kwargs = {
            "api_key": self.config.llm_api_key,
            "timeout": 50
        }
        
        if self.config.llm_base_url:
            client_kwargs["base_url"] = self.config.llm_base_url
        
        self.client = openai.AsyncOpenAI(**client_kwargs)
        logger.info("OpenAI客户端初始化成功")

    def _init_mock_client(self):
        """初始化Mock客户端"""
        self.mock_client = MockClient(self.config)

    def set_event_broadcaster(self, broadcaster):
        """设置事件广播器"""
        self.event_broadcaster = broadcaster

    async def _broadcast_llm_event(self, event_type: str, data: dict):
        """广播LLM事件"""
        if self.event_broadcaster:
            await self.event_broadcaster({
                "type": event_type,
                **data
            })

    def start_new_session(self,  system_prompt:str = None):
        """开始一个新的会话"""        
        self.messages = [
            Message(role="system", content=system_prompt)
        ]
        
    # 有记录的对话
    async def session_completion(self, user_prompt: str,
        tools: Optional[List[Dict[str, Any]]] = None
    ) -> LLMResponse:
        """聊天完成，每次重新组织消息为2个Message：系统提示词 + 拼接的历史对话"""
        if user_prompt:
            self.messages.append(Message(role="user", content=user_prompt))
        # 检查消息长度是否超过限制
        current_length = self._calculate_messages_length()
        max_input_length = self.config.llm_max_input

        if current_length > max_input_length:
            logger.info(f"消息长度 ({current_length}) 超过限制 ({max_input_length})，开始汇总历史消息")
            await self._compress_messages()

        # 重新组织消息：保留第1个系统提示词，后面的消息拼接为一个user Message
        reorganized_messages = self._reorganize_messages_for_completion()

        llm_response = await self.chat_completion(reorganized_messages, tools)

        llm_response.content = self._remove_think(llm_response.content)
        #if llm_response.content:
        self.messages.append(Message(role="assistant",
                            content=f"{llm_response.content} {llm_response.tool_calls}"))

        return llm_response

    def _reorganize_messages_for_completion(self) -> List[Message]:
        """重新组织消息为2个Message：系统提示词 + 拼接的历史对话"""
        if not self.messages:
            return []

        # 保留第1个系统提示词message
        system_message = None
        other_messages = []

        for i, message in enumerate(self.messages):
            if i == 0 and message.role == "system":
                system_message = message
            else:
                other_messages.append(message)

        # 如果没有其他消息，只返回系统消息
        if not other_messages:
            return [system_message] if system_message else []

        # 将其他消息拼接为一个user Message的content，保留原message的角色
        combined_content_parts = ["用户与大模型之间的交互记录如下："]
        for message in other_messages:
            role_prefix = f"{message.role}请求：" if message.role == "user" else f"{message.role}回复："
            combined_content_parts.append(f"- {role_prefix}{message.content}")

        combined_content = "\n".join(combined_content_parts)

        # 创建拼接后的user消息
        combined_user_message = Message(role="user", content=combined_content)

        # 返回重新组织的消息列表
        result = []
        if system_message:
            result.append(system_message)
        result.append(combined_user_message)

        return result

    async def chat_completion(self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None
    ) -> LLMResponse:
        """聊天完成"""
        try:
            if self.config.llm_provider == "openai":
                llm_response = await self._openai_chat_completion(messages, tools)
            elif self.config.llm_provider == "mock":
                llm_response = await self.mock_client.chat_completion(messages, tools)
            else:
                raise ValueError(f"不支持的LLM提供商: {self.config.llm_provider}")
            
            return llm_response

        except Exception as e:
            logger.error(f"LLM聊天完成失败: {e}")
            raise
    
    async def _openai_chat_completion(self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = True
    ) -> LLMResponse:
        """OpenAI聊天完成"""
        # 获取最后一条用户消息，加nothink
        last_msg = messages[len(messages) - 1]
        if last_msg.role == "user":
            last_msg.content = f"{last_msg.content}  {self.config.llm_nothink}"
        start_time = time.time()

        # 转换消息格式
        openai_messages = [
            {"role": msg.role, "content": msg.content}
            for msg in messages
        ]

        # 构建请求参数
        request_params = {
            "model": self.config.llm_model_name,
            "messages": openai_messages,
            "max_tokens": self.config.llm_max_tokens,
            "temperature": self.config.llm_temperature,
            "stream": stream
        }

        # 添加工具
        if tools:
            request_params["tools"] = tools
            request_params["tool_choice"] = "auto"

        # 记录请求
        self.debug_logger.log_llm_request(
            self.config.llm_provider,
            self.config.llm_model_name,
            openai_messages,
            tools
        )

        # 广播LLM请求事件
        await self._broadcast_llm_event("llm_request", {
            "provider": self.config.llm_provider,
            "model": self.config.llm_model_name,
            "message_count": len(openai_messages),
            "estimated_input_tokens": sum(len(str(msg.get("content", ""))) for msg in openai_messages) // 4,
            "has_tools": bool(tools),
            "tool_count": len(tools) if tools else 0,
            "messages": openai_messages,
            "tools": tools if tools else []
        })
        llm_response = LLMResponse(
            content = "",
            tool_calls = [],
        )

        try:
            # 发送请求
            response = await self.client.chat.completions.create(**request_params)

            tool_calls = []
            if stream:
                # 流式响应处理
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.content:
                        content += chunk.choices[0].delta.content
                
                llm_response.response_time = time.time() - start_time
                # 记录原始的响应
                self.debug_logger.log_llm_response(llm_response)

                # 广播LLM响应事件
                await self._broadcast_llm_event("llm_response", {
                    "response_content": llm_response.content,
                    "response_length": len(llm_response.content),
                    "response_time_seconds": llm_response.response_time,
                    "usage": llm_response.usage or {},
                    "tool_calls": llm_response.tool_calls,
                })
                
                # 解析content中的<tool_call> ... </tool_call>
                tool_calls_pattern = r'<tool_call>(.*?)</tool_call>'
                tool_calls_matches = re.findall(tool_calls_pattern, content, re.DOTALL)
                
                # 解析所有工具调用
                for match in tool_calls_matches:
                    try:
                        # 逐个解析JSON并合并
                        parsed = json.loads(match.strip())
                        if isinstance(parsed, list):
                            tool_calls.extend(parsed)
                        else:
                            tool_calls.append(parsed)
                        content = content.replace(f'<tool_call>{match}</tool_call>', '')
                    except json.JSONDecodeError as e:
                        logger.error(f"解析工具调用JSON失败: {e}, 内容: {match}")
                        llm_response.content = f"解析工具调用JSON失败: {e}, 内容: {match}"

                #
                llm_response.content=content
            else:
                llm_response.response_time = time.time() - start_time
                # 非流式响应
                resp_msg = response.choices[0].message
                llm_response.content=resp_msg.content
                llm_response.usage = response.usage.dict() if response.usage else None
                # 记录响应
                self.debug_logger.log_llm_response(llm_response)

                # 广播LLM响应事件
                await self._broadcast_llm_event("llm_response", {
                    "response_content": llm_response.content,
                    "response_length": len(llm_response.content),
                    "response_time_seconds": llm_response.response_time,
                    "usage": llm_response.usage or {},
                    "tool_calls": llm_response.tool_calls,
                })
           
                if resp_msg.tool_calls:
                    tool_calls=[
                            {
                                "id": tc.id,
                                #"function": {
                                    "name": tc.function.name,
                                    "arguments": json.loads(tc.function.arguments) if tc.function.arguments else ""
                                #}
                                ,
                                "type": "function"
                            } for tc in resp_msg.tool_calls
                        ]
                    
            llm_response.tool_calls=tool_calls            
            return llm_response
        except BaseException as e:
            llm_response.response_time = time.time() - start_time

            # 记录错误
            self.debug_logger.log_llm_error(
                self.config.llm_provider,
                self.config.llm_model_name,
                str(e),
                request_params
            )

            # 广播LLM错误事件
            await self._broadcast_llm_event("llm_error", {
                "provider": self.config.llm_provider,
                "model": self.config.llm_model_name,
                "error": str(e),
                "request_data": request_params
            })

            raise

    async def call_tool(self, tool_call_data:Dict) -> ToolResult:
        try:
            # 创建工具调用对象
            tool_call = ToolCall(
                    tool_name=tool_call_data.get("name", tool_call_data.get("function")),
                    #arguments=self._parse_tool_arguments(tool_call_data.get("arguments", ""))
                    arguments=tool_call_data.get("arguments"),
                    call_id=tool_call_data.get("id")
            )

            # 执行工具
            result = await self.tool_manager.execute_tool(tool_call)

        except Exception as e:
                logger.error(f"执行工具调用失败: {e}")
                result = ToolResult(error= f"执行工具调用失败: {e}", success = False)
        
        self.messages.append(
            Message(role="assistant",
            content=f"已调用工具 {tool_call.tool_name}, 结果: {result.result}")
        )
        return result
        
    async def generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入"""
        try:
            if self.config.llm_provider == "openai":
                response = await self.client.embeddings.create(
                    model="text-embedding-ada-002",
                    input=text
                )
                return response.data[0].embedding
            elif self.config.llm_provider == "mock":
                # 生成Mock嵌入向量（1536维，与OpenAI兼容）
                return [random.uniform(-1, 1) for _ in range(1536)]
            else:
                logger.warning(f"嵌入生成不支持提供商: {self.config.llm_provider}")
                return []

        except Exception as e:
            logger.error(f"生成嵌入失败: {e}")
            return []
    
    async def build_summary(self) -> List[str]:
        """对当前会话信息进行总结，返回历史会话记录"""
        if len(self.messages) < 2:
            return self.round_summary
        system_prompt = """你是一个专业的C/C++调试专家，通过使用gdb对执行程序进行多轮调试发现问题。你需要对每轮调试的结果进行总结，包括：
1. 本轮调试的主要发现和进展
2. 执行的关键调试步骤
3. 遇到的问题和障碍
4. 对下一轮调试的建议
5. 错误定位的进展情况

请提供简洁但全面的总结，重点关注对后续调试有价值的信息。"""
        user_prompt = f"请总结以下调试结果：\n"
        for message in self.messages[1:]:
            user_prompt += f"{message.role}: {message.content}\n"
        messages = [
            Message( role="system", content=system_prompt),
            Message( role="user", content=user_prompt)
        ]
        
        llm_response = await self._openai_chat_completion(messages)
        self.round_summary.append(llm_response.content)
        return self.round_summary
    
    def create_debugging_prompt(
        self, 
        error_description: str,
        debug_state: Optional[Dict[str, Any]] = None,
        code_context: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """创建调试提示"""
        prompt_parts = [
            f"程序出现以下错误: {error_description}",
            ""
        ]
        
        # 添加调试状态
        if debug_state:
            prompt_parts.extend([
                "当前调试状态:",
                f"- 程序运行状态: {'运行中' if debug_state.get('is_running') else '已停止'}",
                f"- 停止原因: {debug_state.get('stop_reason', '未知')}"
            ])
            
            # 添加当前栈帧
            if debug_state.get('current_frame'):
                frame = debug_state['current_frame']
                prompt_parts.extend([
                    f"- 当前函数: {frame.get('function', '未知')}",
                    f"- 文件位置: {frame.get('file', '未知')}:{frame.get('line', '未知')}"
                ])
            
            # 添加变量信息
            if debug_state.get('variables'):
                prompt_parts.append("- 局部变量:")
                for name, var in debug_state['variables'].items():
                    prompt_parts.append(f"  {name} = {var.get('value', '未知')}")
            
            prompt_parts.append("")
        
        # 添加代码上下文
        if code_context:
            prompt_parts.extend([
                "相关代码上下文:",
                ""
            ])
            
            for i, context in enumerate(code_context[:3]):  # 限制显示前3个
                prompt_parts.extend([
                    f"代码块 {i+1}:",
                    f"文件: {context.get('file_path', '未知')}",
                    f"函数: {', '.join(context.get('symbols', []))}",
                    "代码:",
                    "``c",
                    context.get('content', ''),
                    "```",
                    ""
                ])
        
        prompt_parts.extend([
            "请分析这个错误，并提供以下信息:",
            "1. 可能的错误原因",
            "2. 建议的调试步骤",
            "3. 需要检查的变量和内存位置",
            "4. 应该设置的断点位置",
            "",
            "请给出具体、可执行的调试建议。"
        ])
        
        return "\n".join(prompt_parts)
        
    def _remove_think(self, text):
        """
        移除字符串中所有<ai>和</ai>标签及其之间的内容。

        参数:
            text (str): 输入的字符串

        返回:
            str: 处理后的字符串
        """
        pattern = r'<think>.*?</think>'
        while True:
            new_text = re.sub(pattern, '', text, flags=re.DOTALL)
            if new_text == text:
                break
            text = new_text
        if all(c in ('\n', ' ') for c in text):
            text = text.replace('\n', '').replace(' ', '')
        return text

    def _calculate_messages_length(self) -> int:
        """计算消息列表的总长度（字符数）"""
        total_length = 0
        for message in self.messages:
            # 计算消息内容长度
            content_length = len(message.content) if message.content else 0
            # 加上角色标识的长度
            role_length = len(message.role) + 10  # 额外的格式字符
            total_length += content_length + role_length

        return total_length

    async def _summarize_messages(self, messages_to_summarize: List[Message]) -> str:
        """将消息列表汇总为简洁的历史记录"""
        if not messages_to_summarize:
            return "无历史记录"

        # 构建汇总提示
        system_prompt = """你是一个专业的AI调试助手。请将以下调试会话历史汇总为简洁的记录，包括：
1. 主要的调试步骤和发现
2. 重要的工具调用和结果
3. 关键的分析结论
4. 遇到的问题和解决方案

请保持汇总简洁明了，突出重点信息，去除冗余内容。汇总应该帮助后续的调试工作。"""

        # 构建要汇总的内容
        content_to_summarize = "调试会话历史:\n\n"
        for i, msg in enumerate(messages_to_summarize, 1):
            content_to_summarize += f"{i}. [{msg.role}]: {msg.content}\n\n"

        # 创建汇总消息
        summary_messages = [
            Message(role="system", content=system_prompt),
            Message(role="user", content=content_to_summarize)
        ]

        try:
            # 调用LLM进行汇总
            logger.info(f"正在汇总 {len(messages_to_summarize)} 条消息...")
            response = await self.chat_completion(summary_messages)

            summary = response.content.strip()
            if not summary:
                summary = f"历史记录汇总：包含 {len(messages_to_summarize)} 条调试消息"

            logger.info(f"消息汇总完成，原始长度: {sum(len(msg.content) for msg in messages_to_summarize)}, 汇总长度: {len(summary)}")
            return summary

        except Exception as e:
            logger.error(f"消息汇总失败: {e}")
            # 回退到简单汇总
            return f"调试历史记录汇总（{len(messages_to_summarize)}条消息）：包含调试步骤、工具调用和分析结果"

    async def _compress_messages(self):
        """压缩消息列表，保留系统提示词和最新的2个消息，汇总中间的消息"""
        if len(self.messages) <= 3:  # 系统消息 + 最多2个消息，无需压缩
            return

        # 保留系统消息（第一个消息）
        system_message = self.messages[0] if self.messages and self.messages[0].role == "system" else None

        # 保留最新的2个消息
        recent_messages = self.messages[-2:] if len(self.messages) >= 2 else self.messages[-1:]

        # 需要汇总的中间消息
        if system_message:
            messages_to_summarize = self.messages[1:-2] if len(self.messages) > 3 else []
        else:
            messages_to_summarize = self.messages[:-2] if len(self.messages) > 2 else []

        if not messages_to_summarize:
            return

        # 汇总中间消息
        summary = await self._summarize_messages(messages_to_summarize)

        # 重构消息列表
        new_messages = []

        # 添加系统消息
        if system_message:
            new_messages.append(system_message)

        # 添加汇总消息
        summary_message = Message(
            role="assistant",
            content=f"[本轮调试历史记录汇总] {summary}"
        )
        new_messages.append(summary_message)

        # 添加最新的消息
        new_messages.extend(recent_messages)

        # 更新消息列表
        old_count = len(self.messages)
        self.messages = new_messages
        new_count = len(self.messages)

        logger.info(f"消息压缩完成：{old_count} -> {new_count} 条消息")

        # 重新计算长度
        new_length = self._calculate_messages_length()
        logger.info(f"压缩后消息长度: {new_length}")

        # 如果压缩后仍然超长，可能需要进一步处理
        if new_length > self.config.llm_max_input:
            logger.warning(f"压缩后消息长度仍超过限制，当前: {new_length}, 限制: {self.config.llm_max_input}")
            # 可以考虑进一步压缩或截断最新消息
