{% extends "base.html" %}

{% block title %}AI-GDB 调试界面{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧调试面板 -->
    <div class="col-md-6">
        <!-- 调试控制 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-play-circle"></i> 调试控制</h5>
            </div>
            <div class="card-body">
                <!-- 调试会话启动 -->
                <div class="mb-3">
                    <label for="fault-description" class="form-label">故障描述</label>
                    <textarea
                        class="form-control"
                        id="fault-description"
                        rows="3"
                        placeholder="请描述您遇到的问题或需要调试的故障..."
                    ></textarea>
                </div>

                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="start-debug-btn">
                        <i class="fas fa-play"></i> 开始调试
                    </button>
                    <button type="button" class="btn btn-danger" id="stop-debug-btn" disabled>
                        <i class="fas fa-stop"></i> 停止调试
                    </button>
                </div>
            </div>
        </div>

        <!-- 调试状态 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 调试状态</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>当前状态</h6>
                    <div id="debug-status">
                        <span class="badge bg-secondary">未开始</span>
                    </div>
                </div>

                <!-- 当前轮次 -->
                <div class="mb-3">
                    <h6>调试轮次</h6>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="debug-progress">
                            0 / 10
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 断点列表 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-crosshairs"></i> 断点列表</h5>
            </div>
            <div class="card-body">
                <div class="breakpoint-list" id="breakpoint-list">
                    <p class="text-muted">暂无断点</p>
                </div>
            </div>
        </div>

        <!-- 调试输出 -->
        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-terminal"></i> 调试输出</h5>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="clear-output-btn">
                    <i class="fas fa-trash"></i> 清空
                </button>
            </div>
            <div class="card-body p-0">
                <div class="debug-output" id="debug-output" style="height: 300px; overflow-y: auto;">
                    <div class="text-muted p-3">等待调试输出...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧LLM交互区域 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-robot"></i> AI 交互记录</h5>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="clear-llm-log-btn">
                    <i class="fas fa-trash"></i> 清空
                </button>
            </div>
            <div class="card-body p-0">
                <div class="llm-interaction-log" id="llm-interaction-log" style="height: 600px; overflow-y: auto;">
                    <div class="text-muted p-3 text-center">
                        <i class="fas fa-robot fa-3x mb-3"></i>
                        <p>等待AI交互...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
// 全局变量
let debugLogger = null;

$(document).ready(function() {
    console.log('调试页面初始化开始...');

    // 检查依赖
    if (typeof AIGDB === 'undefined') {
        console.error('AIGDB对象未定义，请检查app.js是否正确加载');
        alert('页面初始化失败：缺少必要的JavaScript库');
        return;
    }

    if (typeof window.wsManager === 'undefined') {
        console.warn('WebSocket管理器未初始化，将在连接建立后重试');
    }

    try {
        // 初始化组件
        initializeComponents();
        console.log('组件初始化完成');

        // 绑定事件处理器
        bindEventHandlers();
        console.log('事件处理器绑定完成');

        // 连接到后端并加载当前会话信息
        loadCurrentSession();
        console.log('会话信息加载开始');

        // 监听调试状态变化
        document.addEventListener('debug_status_changed', handleDebugStatusChange);
        console.log('调试页面初始化完成');

    } catch (error) {
        console.error('调试页面初始化失败:', error);
        alert('页面初始化失败: ' + error.message);
    }
});

function initializeComponents() {
    // 初始化调试日志
    debugLogger = new AIGDB.components.Logger('debug-log');
}

function bindEventHandlers() {
    // 开始调试
    $('#start-debug-btn').click(function() {
        console.log('开始调试按钮被点击');

        const description = $('#fault-description').val().trim();
        console.log('故障描述:', description);

        if (!description) {
            console.warn('故障描述为空');
            if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                AIGDB.utils.showNotification('请输入故障描述', 'warning');
            } else {
                alert('请输入故障描述');
            }
            return;
        }

        console.log('开始启动调试会话...');
        startDebugSession(description);
    });

    // 停止调试
    $('#stop-debug-btn').click(function() {
        stopDebugSession();
    });

    // 清空调试输出
    $('#clear-output-btn').click(function() {
        $('#debug-output').html('<div class="text-muted p-3">等待调试输出...</div>');
    });

    // 清空LLM交互日志
    $('#clear-llm-log-btn').click(function() {
        $('#llm-interaction-log').html(`
            <div class="text-muted p-3 text-center">
                <i class="fas fa-robot fa-3x mb-3"></i>
                <p>等待AI交互...</p>
            </div>
        `);
    });
}

function loadCurrentSession() {
    // 连接到后端并加载当前会话信息
    if (window.wsManager) {
        wsManager.send('get_status', {});
        console.log('请求当前会话状态');
    }
}

function loadBreakpoints() {
    // 加载当前会话的断点列表
    if (window.wsManager) {
        wsManager.send('get_breakpoints', {});
        console.log('请求断点列表');
    }
}

function updateBreakpointList(breakpoints) {
    const container = $('#breakpoint-list');

    if (!breakpoints || breakpoints.length === 0) {
        container.html('<p class="text-muted">暂无断点</p>');
        return;
    }

    let html = '';
    breakpoints.forEach((bp, index) => {
        html += `
            <div class="breakpoint-item mb-2 p-2 border rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${bp.location}</strong>
                        ${bp.condition ? `<br><small class="text-muted">条件: ${bp.condition}</small>` : ''}
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-breakpoint-btn"
                            data-location="${bp.location}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    });

    container.html(html);

    // 绑定删除断点事件
    $('.remove-breakpoint-btn').click(function() {
        const location = $(this).data('location');
        removeBreakpoint(location);
    });
}

function removeBreakpoint(location) {
    if (window.wsManager) {
        wsManager.send('remove_breakpoint', { location: location });
        console.log('删除断点:', location);
    }
}

function startDebugSession(description) {
    console.log('startDebugSession被调用，描述:', description);

    // 检查WebSocket连接
    if (!window.wsManager) {
        console.error('WebSocket管理器未初始化');
        const message = 'WebSocket连接未建立，请刷新页面重试';
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'error');
        } else {
            alert(message);
        }
        return;
    }

    // 检查WebSocket连接状态
    const wsState = wsManager.getState();
    console.log('WebSocket状态:', wsState);

    if (wsState !== 'OPEN') {
        console.error('WebSocket连接未就绪，状态:', wsState);
        const message = 'WebSocket连接未就绪，请等待连接建立';
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'warning');
        } else {
            alert(message);
        }
        return;
    }

    try {
        // 发送调试启动消息
        console.log('发送start_debug消息: ' + description);
        const success = wsManager.send('start_debug', { fault_description: description });

        if (success) {
            console.log('start_debug消息发送成功');

            // 记录日志
            if (debugLogger) {
                debugLogger.log('开始调试会话: ' + description, 'info');
            }

            // 显示成功通知
            if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                AIGDB.utils.showNotification('调试会话启动中...', 'info');
            }

            // 更新UI状态
            $('#start-debug-btn').prop('disabled', true).text('启动中...');

        } else {
            console.error('start_debug消息发送失败');
            const message = '发送调试启动消息失败';
            if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                AIGDB.utils.showNotification(message, 'error');
            } else {
                alert(message);
            }
        }

    } catch (error) {
        console.error('启动调试会话时发生错误:', error);
        const message = '启动调试会话失败: ' + error.message;
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
}

function stopDebugSession() {
    if (window.wsManager) {
        wsManager.send('stop_debug', {});
        debugLogger.log('停止调试会话', 'info');
    }
}

function appendLLMInteraction(type, data) {
    const container = $('#llm-interaction-log');
    const timestamp = new Date().toLocaleTimeString();

    // 移除初始提示信息
    if (container.find('.text-muted').length > 0) {
        container.empty();
    }

    let html = '';
    let iconClass = '';
    let bgClass = '';

    switch (type) {
        case 'llm_request':
            iconClass = 'fas fa-arrow-up text-primary';
            bgClass = 'bg-light border-start border-primary border-3';
            html = `
                <div class="llm-interaction-item mb-3 p-3 ${bgClass}">
                    <div class="d-flex align-items-center mb-2">
                        <i class="${iconClass}"></i>
                        <strong class="ms-2">AI 请求</strong>
                        <small class="text-muted ms-auto">${timestamp}</small>
                    </div>
                    <div class="llm-content">
                        <div class="mb-2">
                            <strong>模型:</strong> ${data.model || 'Unknown'}
                            <span class="ms-3"><strong>消息数:</strong> ${data.message_count || 0}</span>
                            ${data.estimated_input_tokens ? `<span class="ms-3"><strong>预估Token:</strong> ${data.estimated_input_tokens}</span>` : ''}
                        </div>
                        ${data.messages && data.messages.length > 0 ? `
                            <div class="messages-preview">
                                <strong>最后消息:</strong>
                                <div class="bg-white p-2 rounded border mt-1" style="max-height: 150px; overflow-y: auto;">
                                    <small>${data.messages[data.messages.length - 1].content.substring(0, 300)}${data.messages[data.messages.length - 1].content.length > 300 ? '...' : ''}</small>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            break;

        case 'llm_response':
            iconClass = 'fas fa-arrow-down text-success';
            bgClass = 'bg-light border-start border-success border-3';
            html = `
                <div class="llm-interaction-item mb-3 p-3 ${bgClass}">
                    <div class="d-flex align-items-center mb-2">
                        <i class="${iconClass}"></i>
                        <strong class="ms-2">AI 响应</strong>
                        <small class="text-muted ms-auto">${timestamp}</small>
                    </div>
                    <div class="llm-content">
                        <div class="mb-2">
                            <strong>响应时间:</strong> ${data.response_time_seconds ? (data.response_time_seconds.toFixed(2) + 's') : 'Unknown'}
                            ${data.usage && data.usage.total_tokens ? `<span class="ms-3"><strong>Token使用:</strong> ${data.usage.total_tokens}</span>` : ''}
                        </div>
                        ${data.response_content ? `
                            <div class="response-content">
                                <strong>响应内容:</strong>
                                <div class="bg-white p-2 rounded border mt-1" style="max-height: 200px; overflow-y: auto;">
                                    <small>${data.response_content.substring(0, 500)}${data.response_content.length > 500 ? '...' : ''}</small>
                                </div>
                            </div>
                        ` : ''}
                        ${data.tool_calls && data.tool_calls.length > 0 ? `
                            <div class="tool-calls mt-2">
                                <strong>工具调用:</strong> ${data.tool_calls.length} 个
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            break;

        case 'llm_error':
            iconClass = 'fas fa-exclamation-triangle text-danger';
            bgClass = 'bg-light border-start border-danger border-3';
            html = `
                <div class="llm-interaction-item mb-3 p-3 ${bgClass}">
                    <div class="d-flex align-items-center mb-2">
                        <i class="${iconClass}"></i>
                        <strong class="ms-2">AI 错误</strong>
                        <small class="text-muted ms-auto">${timestamp}</small>
                    </div>
                    <div class="llm-content">
                        <div class="mb-2">
                            <strong>模型:</strong> ${data.model || 'Unknown'}
                        </div>
                        <div class="error-message">
                            <strong>错误信息:</strong>
                            <div class="bg-white p-2 rounded border mt-1 text-danger">
                                <small>${data.error}</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            break;
    }

    container.append(html);

    // 滚动到底部
    container.scrollTop(container[0].scrollHeight);
}

function handleDebugStatusChange(event) {
    const data = event.detail;
    updateDebugStatus(data);
}

function updateDebugStatus(data) {
    console.log('updateDebugStatus被调用，数据:', data);

    // 处理数据结构，支持直接数据和嵌套数据
    const statusData = data.data || data;

    // 验证数据有效性
    if (!statusData || typeof statusData !== 'object') {
        console.warn('状态数据无效:', statusData);
        return;
    }

    console.log('处理后的状态数据:', statusData);

    // 更新调试状态
    const statusElement = $('#debug-status');
    const isDebugging = statusData.is_debugging;
    const currentRound = statusData.current_round || 0;
    const maxRounds = statusData.max_rounds || statusData.max_debug_rounds || 10;

    if (isDebugging) {
        statusElement.html('<span class="badge bg-success">调试中</span>');
        $('#start-debug-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 调试中');
        $('#stop-debug-btn').prop('disabled', false);
    } else {
        statusElement.html('<span class="badge bg-secondary">未开始</span>');
        $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
        $('#stop-debug-btn').prop('disabled', true);
    }

    // 更新进度条
    const progress = maxRounds > 0 ? (currentRound / maxRounds) * 100 : 0;
    $('#debug-progress')
        .css('width', progress + '%')
        .text(`${currentRound} / ${maxRounds}`);

    console.log(`状态更新完成: 调试中=${isDebugging}, 轮次=${currentRound}/${maxRounds}, 进度=${progress.toFixed(1)}%`);

    // 加载断点列表
    if (statusData.breakpoints) {
        updateBreakpointList(statusData.breakpoints);
    }
}

// WebSocket消息处理
function setupWebSocketHandlers() {
    if (!window.wsManager) {
        console.warn('WebSocket管理器未初始化，延迟设置消息处理器');
        setTimeout(setupWebSocketHandlers, 1000);
        return;
    }

    console.log('设置WebSocket消息处理器...');

    // 监听状态更新消息
    wsManager.on('status_update', function(data) {
        console.log('收到status_update消息:', data);
        updateDebugStatus(data);
    });

    // 监听调试相关消息
    wsManager.on('debug_output', function(data) {
        console.log('收到debug_output消息:', data);
        appendDebugOutput(data.output, data.output_type || 'info');
    });

    wsManager.on('breakpoint_hit', function(data) {
        console.log('收到breakpoint_hit消息:', data);
        if (debugLogger) {
            debugLogger.log(`断点命中: ${data.location}`, 'warning');
        }
        appendDebugOutput(`断点命中: ${data.location}`, 'warning');
    });

    // 监听断点列表更新
    wsManager.on('breakpoints_update', function(data) {
        console.log('收到breakpoints_update消息:', data);
        updateBreakpointList(data.breakpoints);
    });

    // 监听LLM交互消息
    wsManager.on('llm_request', function(data) {
        console.log('收到llm_request消息:', data);
        appendLLMInteraction('llm_request', data);
    });

    wsManager.on('llm_response', function(data) {
        console.log('收到llm_response消息:', data);
        appendLLMInteraction('llm_response', data);
    });

    wsManager.on('llm_error', function(data) {
        console.log('收到llm_error消息:', data);
        appendLLMInteraction('llm_error', data);
    });

    // 监听调试会话响应
    wsManager.on('response', function(data) {
        console.log('收到response消息:', data);

        // 检查是否是start_debug的响应
        if (data.data && data.data.type === 'start_debug') {
            const responseData = data.data;

            if (responseData.success) {
                console.log('调试会话启动成功');
                $('#start-debug-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 调试中');
                $('#stop-debug-btn').prop('disabled', false);

                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification('调试会话已启动', 'success');
                }

                if (debugLogger) {
                    debugLogger.log('调试会话启动成功', 'success');
                }
            } else {
                console.error('调试会话启动失败:', responseData.error);

                // 恢复按钮状态
                $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
                $('#stop-debug-btn').prop('disabled', true);

                const message = '调试会话启动失败: ' + (responseData.error || '未知错误');

                // 显示错误通知
                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification(message, 'error');
                } else {
                    alert(message);
                }

                // 记录到调试日志
                if (debugLogger) {
                    debugLogger.log(message, 'error');
                }
            }
        }

        // 检查是否是stop_debug的响应
        if (data.data && data.data.type === 'stop_debug') {
            const responseData = data.data;
            console.log('收到stop_debug响应:', responseData);

            if (responseData.success) {
                console.log('调试会话停止成功');

                // 更新按钮状态
                $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
                $('#stop-debug-btn').prop('disabled', true);

                // 更新调试状态显示
                $('#debug-status').html('<span class="badge bg-secondary">未开始</span>');

                // 重置进度条
                $('#debug-progress').css('width', '0%').text('0 / 10');

                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification('调试会话已停止', 'info');
                }

                if (debugLogger) {
                    debugLogger.log('调试会话停止成功', 'info');
                }
            } else {
                console.error('调试会话停止失败:', responseData.error);

                const message = '调试会话停止失败: ' + (responseData.error || '未知错误');

                // 显示错误通知
                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification(message, 'error');
                } else {
                    alert(message);
                }

                // 记录到调试日志
                if (debugLogger) {
                    debugLogger.log(message, 'error');
                }
            }
        }
    });

    // 监听错误消息
    wsManager.on('error', function(data) {
        console.error('收到WebSocket错误消息:', data);

        // 恢复按钮状态
        $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
        $('#stop-debug-btn').prop('disabled', true);

        const message = 'WebSocket错误: ' + (data.error || '未知错误');

        // 显示错误通知
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'error');
        }

        // 记录到调试日志
        if (debugLogger) {
            debugLogger.log(message, 'error');
        }
    });

    console.log('WebSocket消息处理器设置完成');
}

// 在页面加载时和WebSocket连接建立时设置处理器
setupWebSocketHandlers();

// 监听WebSocket连接事件
document.addEventListener('ws_connected', function() {
    console.log('WebSocket连接已建立，重新设置消息处理器');
    setupWebSocketHandlers();
});

// 调试工具：手动获取状态
function debugGetStatus() {
    console.log('手动获取状态...');
    if (window.wsManager) {
        wsManager.send('get_status', {});
    } else {
        console.error('WebSocket管理器未初始化');
    }
}

// 调试工具：显示当前状态
function debugShowCurrentStatus() {
    console.log('=== 当前状态信息 ===');
    console.log('调试状态元素:', $('#debug-status').html());
    console.log('开始按钮状态:', $('#start-debug-btn').prop('disabled'), $('#start-debug-btn').html());
    console.log('停止按钮状态:', $('#stop-debug-btn').prop('disabled'));
    console.log('进度条:', $('#debug-progress').css('width'), $('#debug-progress').text());
    console.log('WebSocket状态:', window.wsManager ? wsManager.getState() : '未初始化');
}

// 将调试函数暴露到全局
window.debugGetStatus = debugGetStatus;
window.debugShowCurrentStatus = debugShowCurrentStatus;

function appendDebugOutput(text, type = 'info') {
    const outputElement = $('#debug-output');
    const timestamp = new Date().toLocaleTimeString();
    const className = `gdb-${type}`;
    
    const entry = `<div class="${className}">[${timestamp}] ${text}</div>`;
    
    console.log('=== appendDebugOutput : ' + entry);
    if (outputElement.find('.text-muted').length > 0) {
        outputElement.html(entry);
    } else {
        outputElement.append(entry);
    }
    
    // 滚动到底部
    outputElement.scrollTop(outputElement[0].scrollHeight);
}


</script>
{% endblock %}
