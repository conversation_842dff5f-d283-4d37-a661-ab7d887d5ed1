{% extends "base.html" %}

{% block title %}AI-GDB 调试界面{% endblock %}

{% block content %}
<div class="row">
    <!-- 调试控制面板 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-play-circle"></i> 调试控制</h5>
            </div>
            <div class="card-body">
                <!-- 调试会话启动 -->
                <div class="mb-3">
                    <label for="fault-description" class="form-label">故障描述</label>
                    <textarea 
                        class="form-control" 
                        id="fault-description" 
                        rows="3" 
                        placeholder="请描述您遇到的问题或需要调试的故障..."
                    ></textarea>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="start-debug-btn">
                        <i class="fas fa-play"></i> 开始调试
                    </button>
                    <button type="button" class="btn btn-danger" id="stop-debug-btn" disabled>
                        <i class="fas fa-stop"></i> 停止调试
                    </button>
                </div>
                
                <hr>
                
                <!-- 调试状态 -->
                <div class="mb-3">
                    <h6>调试状态</h6>
                    <div id="debug-status">
                        <span class="badge bg-secondary">未开始</span>
                    </div>
                </div>
                
                <!-- 当前轮次 -->
                <div class="mb-3">
                    <h6>调试轮次</h6>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%" id="debug-progress">
                            0 / 10
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 断点管理 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-crosshairs"></i> 断点管理</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="breakpoint-location" class="form-label">断点位置</label>
                    <input type="text" class="form-control" id="breakpoint-location" 
                           placeholder="文件名:行号 或 函数名">
                </div>
                <div class="mb-3">
                    <label for="breakpoint-condition" class="form-label">条件 (可选)</label>
                    <input type="text" class="form-control" id="breakpoint-condition" 
                           placeholder="例如: x > 10">
                </div>
                <button type="button" class="btn btn-primary btn-sm" id="add-breakpoint-btn">
                    <i class="fas fa-plus"></i> 添加断点
                </button>
                
                <div class="mt-3">
                    <h6>当前断点</h6>
                    <div class="breakpoint-list" id="breakpoint-list">
                        <p class="text-muted">暂无断点</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 代码编辑器和输出 -->
    <div class="col-md-8">
        <!-- 文件浏览器 -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-folder-open"></i> 项目文件</h5>
            </div>
            <div class="card-body">
                <div class="file-tree" id="file-browser">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 代码编辑器 -->
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-code"></i> 代码编辑器</h5>
                <span id="current-file-name" class="text-muted">未选择文件</span>
            </div>
            <div class="card-body p-0">
                <textarea id="code-editor" style="height: 400px;"></textarea>
            </div>
        </div>
        
        <!-- 调试输出 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-terminal"></i> 调试输出</h5>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="clear-output-btn">
                    <i class="fas fa-trash"></i> 清空
                </button>
            </div>
            <div class="card-body p-0">
                <div class="debug-output" id="debug-output">
                    <div class="text-muted p-3">等待调试输出...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 调试控制按钮行 -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-gamepad"></i> 调试控制</h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" id="continue-btn" disabled>
                        <i class="fas fa-play"></i> 继续执行
                    </button>
                    <button type="button" class="btn btn-outline-info" id="step-over-btn" disabled>
                        <i class="fas fa-step-forward"></i> 单步执行
                    </button>
                    <button type="button" class="btn btn-outline-info" id="step-into-btn" disabled>
                        <i class="fas fa-arrow-down"></i> 单步进入
                    </button>
                </div>
                
                <div class="btn-group ms-3" role="group">
                    <button type="button" class="btn btn-outline-secondary" id="get-variables-btn" disabled>
                        <i class="fas fa-list"></i> 获取变量
                    </button>
                </div>
                
                <div class="input-group ms-3" style="width: 300px; display: inline-flex;">
                    <input type="text" class="form-control" id="expression-input" 
                           placeholder="输入表达式..." disabled>
                    <button class="btn btn-outline-secondary" type="button" id="evaluate-btn" disabled>
                        <i class="fas fa-calculator"></i> 计算
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 变量监控 -->
<div class="row mt-3">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-eye"></i> 变量监控</h5>
            </div>
            <div class="card-body">
                <div class="variable-monitor" id="variable-monitor">
                    <p class="text-muted">暂无变量信息</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> 调试日志</h5>
            </div>
            <div class="card-body">
                <div class="log-container" id="debug-log">
                    <!-- 日志内容将通过JavaScript动态添加 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 全局变量
let codeEditor = null;
let fileBrowser = null;
let debugLogger = null;

$(document).ready(function() {
    console.log('调试页面初始化开始...');

    // 检查依赖
    if (typeof AIGDB === 'undefined') {
        console.error('AIGDB对象未定义，请检查app.js是否正确加载');
        alert('页面初始化失败：缺少必要的JavaScript库');
        return;
    }

    if (typeof window.wsManager === 'undefined') {
        console.warn('WebSocket管理器未初始化，将在连接建立后重试');
    }

    try {
        // 初始化组件
        initializeComponents();
        console.log('组件初始化完成');

        // 绑定事件处理器
        bindEventHandlers();
        console.log('事件处理器绑定完成');

        // 加载项目文件
        loadProjectFiles();
        console.log('项目文件加载开始');

        // 监听调试状态变化
        document.addEventListener('debug_status_changed', handleDebugStatusChange);
        console.log('调试页面初始化完成');

    } catch (error) {
        console.error('调试页面初始化失败:', error);
        alert('页面初始化失败: ' + error.message);
    }
});

function initializeComponents() {
    // 初始化代码编辑器
    if (typeof CodeMirror !== 'undefined') {
        codeEditor = CodeMirror.fromTextArea(document.getElementById('code-editor'), {
            lineNumbers: true,
            mode: 'text/x-csrc',
            theme: 'monokai',
            readOnly: true,
            lineWrapping: true
        });
    }
    
    // 初始化文件浏览器
    fileBrowser = new AIGDB.components.FileBrowser('file-browser');
    fileBrowser.onFileSelect = function(filePath) {
        loadFileContent(filePath);
    };
    
    // 初始化调试日志
    debugLogger = new AIGDB.components.Logger('debug-log');
}

function bindEventHandlers() {
    // 开始调试
    $('#start-debug-btn').click(function() {
        console.log('开始调试按钮被点击');

        const description = $('#fault-description').val().trim();
        console.log('故障描述:', description);

        if (!description) {
            console.warn('故障描述为空');
            if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                AIGDB.utils.showNotification('请输入故障描述', 'warning');
            } else {
                alert('请输入故障描述');
            }
            return;
        }

        console.log('开始启动调试会话...');
        startDebugSession(description);
    });
    
    // 停止调试
    $('#stop-debug-btn').click(function() {
        stopDebugSession();
    });
    
    // 添加断点
    $('#add-breakpoint-btn').click(function() {
        const location = $('#breakpoint-location').val().trim();
        if (!location) {
            AIGDB.utils.showNotification('请输入断点位置', 'warning');
            return;
        }
        
        const condition = $('#breakpoint-condition').val().trim();
        addBreakpoint(location, condition);
    });
    
    // 调试控制按钮
    $('#continue-btn').click(() => sendDebugCommand('continue_execution'));
    $('#step-over-btn').click(() => sendDebugCommand('step_over'));
    $('#step-into-btn').click(() => sendDebugCommand('step_into'));
    $('#get-variables-btn').click(() => sendDebugCommand('get_variables'));
    
    // 表达式计算
    $('#evaluate-btn').click(function() {
        const expression = $('#expression-input').val().trim();
        if (expression) {
            evaluateExpression(expression);
        }
    });
    
    // 清空输出
    $('#clear-output-btn').click(function() {
        $('#debug-output').html('<div class="text-muted p-3">等待调试输出...</div>');
    });
}

function loadProjectFiles() {
    if (fileBrowser) {
        fileBrowser.loadFiles('');
    }
}

function loadFileContent(filePath) {
    $.get(`/api/files/content?path=${encodeURIComponent(filePath)}`)
        .done(function(response) {
            if (response.success && codeEditor) {
                codeEditor.setValue(response.data.content);
                $('#current-file-name').text(filePath);
                AIGDB.state.selectedFile = filePath;
            } else {
                AIGDB.utils.showNotification('加载文件失败: ' + response.error, 'error');
            }
        })
        .fail(function() {
            AIGDB.utils.showNotification('加载文件失败', 'error');
        });
}

function startDebugSession(description) {
    console.log('startDebugSession被调用，描述:', description);

    // 检查WebSocket连接
    if (!window.wsManager) {
        console.error('WebSocket管理器未初始化');
        const message = 'WebSocket连接未建立，请刷新页面重试';
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'error');
        } else {
            alert(message);
        }
        return;
    }

    // 检查WebSocket连接状态
    const wsState = wsManager.getState();
    console.log('WebSocket状态:', wsState);

    if (wsState !== 'OPEN') {
        console.error('WebSocket连接未就绪，状态:', wsState);
        const message = 'WebSocket连接未就绪，请等待连接建立';
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'warning');
        } else {
            alert(message);
        }
        return;
    }

    try {
        // 发送调试启动消息
        console.log('发送start_debug消息: ' + description);
        const success = wsManager.send('start_debug', { fault_description: description });

        if (success) {
            console.log('start_debug消息发送成功');

            // 记录日志
            if (debugLogger) {
                debugLogger.log('开始调试会话: ' + description, 'info');
            }

            // 显示成功通知
            if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                AIGDB.utils.showNotification('调试会话启动中...', 'info');
            }

            // 更新UI状态
            $('#start-debug-btn').prop('disabled', true).text('启动中...');

        } else {
            console.error('start_debug消息发送失败');
            const message = '发送调试启动消息失败';
            if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                AIGDB.utils.showNotification(message, 'error');
            } else {
                alert(message);
            }
        }

    } catch (error) {
        console.error('启动调试会话时发生错误:', error);
        const message = '启动调试会话失败: ' + error.message;
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
}

function stopDebugSession() {
    if (window.wsManager) {
        wsManager.send('stop_debug', {});
        debugLogger.log('停止调试会话', 'info');
    }
}

function addBreakpoint(location, condition) {
    if (window.wsManager) {
        wsManager.send('set_breakpoint', { 
            location: location, 
            condition: condition || null 
        });
        debugLogger.log(`添加断点: ${location}${condition ? ' (条件: ' + condition + ')' : ''}`, 'info');
    }
}

function sendDebugCommand(command) {
    if (window.wsManager) {
        wsManager.send(command, {});
        debugLogger.log(`执行调试命令: ${command}`, 'info');
    }
}

function evaluateExpression(expression) {
    if (window.wsManager) {
        wsManager.send('evaluate_expression', { expression: expression });
        debugLogger.log(`计算表达式: ${expression}`, 'info');
    }
}

function handleDebugStatusChange(event) {
    const data = event.detail;
    updateDebugStatus(data);
}

function updateDebugStatus(data) {
    console.log('updateDebugStatus被调用，数据:', data);

    // 处理数据结构，支持直接数据和嵌套数据
    const statusData = data.data || data;

    // 验证数据有效性
    if (!statusData || typeof statusData !== 'object') {
        console.warn('状态数据无效:', statusData);
        return;
    }

    console.log('处理后的状态数据:', statusData);

    // 更新调试状态
    const statusElement = $('#debug-status');
    const isDebugging = statusData.is_debugging;
    const currentRound = statusData.current_round || 0;
    const maxRounds = statusData.max_rounds || statusData.max_debug_rounds || 10;

    if (isDebugging) {
        statusElement.html('<span class="badge bg-success">调试中</span>');
        $('#start-debug-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 调试中');
        $('#stop-debug-btn').prop('disabled', false);
        enableDebugControls(true);
    } else {
        statusElement.html('<span class="badge bg-secondary">未开始</span>');
        $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
        $('#stop-debug-btn').prop('disabled', true);
        enableDebugControls(false);
    }

    // 更新进度条
    const progress = maxRounds > 0 ? (currentRound / maxRounds) * 100 : 0;
    $('#debug-progress')
        .css('width', progress + '%')
        .text(`${currentRound} / ${maxRounds}`);

    console.log(`状态更新完成: 调试中=${isDebugging}, 轮次=${currentRound}/${maxRounds}, 进度=${progress.toFixed(1)}%`);
}

function enableDebugControls(enabled) {
    $('#continue-btn').prop('disabled', !enabled);
    $('#step-over-btn').prop('disabled', !enabled);
    $('#step-into-btn').prop('disabled', !enabled);
    $('#get-variables-btn').prop('disabled', !enabled);
    $('#evaluate-btn').prop('disabled', !enabled);
    $('#expression-input').prop('disabled', !enabled);
}

// WebSocket消息处理
function setupWebSocketHandlers() {
    if (!window.wsManager) {
        console.warn('WebSocket管理器未初始化，延迟设置消息处理器');
        setTimeout(setupWebSocketHandlers, 1000);
        return;
    }

    console.log('设置WebSocket消息处理器...');

    // 监听状态更新消息
    wsManager.on('status_update', function(data) {
        console.log('收到status_update消息:', data);
        updateDebugStatus(data);
    });

    // 监听调试相关消息
    wsManager.on('debug_output', function(data) {
        console.log('收到debug_output消息:', data);
        appendDebugOutput(data.output, data.output_type || 'info');
    });

    wsManager.on('breakpoint_hit', function(data) {
        console.log('收到breakpoint_hit消息:', data);
        if (debugLogger) {
            debugLogger.log(`断点命中: ${data.location}`, 'warning');
        }
        appendDebugOutput(`断点命中: ${data.location}`, 'warning');
    });

    wsManager.on('variables_update', function(data) {
        console.log('收到variables_update消息:', data);
        updateVariableMonitor(data.variables);
    });

    wsManager.on('expression_result', function(data) {
        console.log('收到expression_result消息:', data);
        appendDebugOutput(`${data.expression} = ${data.result}`, 'success');
    });

    // 监听调试会话响应
    wsManager.on('response', function(data) {
        console.log('收到response消息:', data);

        // 检查是否是start_debug的响应
        if (data.data && data.data.type === 'start_debug') {
            const responseData = data.data;

            if (responseData.success) {
                console.log('调试会话启动成功');
                $('#start-debug-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 调试中');
                $('#stop-debug-btn').prop('disabled', false);

                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification('调试会话已启动', 'success');
                }

                if (debugLogger) {
                    debugLogger.log('调试会话启动成功', 'success');
                }
            } else {
                console.error('调试会话启动失败:', responseData.error);

                // 恢复按钮状态
                $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
                $('#stop-debug-btn').prop('disabled', true);

                const message = '调试会话启动失败: ' + (responseData.error || '未知错误');

                // 显示错误通知
                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification(message, 'error');
                } else {
                    alert(message);
                }

                // 记录到调试日志
                if (debugLogger) {
                    debugLogger.log(message, 'error');
                }
            }
        }

        // 检查是否是stop_debug的响应
        if (data.data && data.data.type === 'stop_debug') {
            const responseData = data.data;
            console.log('收到stop_debug响应:', responseData);

            if (responseData.success) {
                console.log('调试会话停止成功');

                // 更新按钮状态
                $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
                $('#stop-debug-btn').prop('disabled', true);

                // 更新调试状态显示
                $('#debug-status').html('<span class="badge bg-secondary">未开始</span>');

                // 禁用调试控制按钮
                enableDebugControls(false);

                // 重置进度条
                $('#debug-progress').css('width', '0%').text('0 / 10');

                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification('调试会话已停止', 'info');
                }

                if (debugLogger) {
                    debugLogger.log('调试会话停止成功', 'info');
                }
            } else {
                console.error('调试会话停止失败:', responseData.error);

                const message = '调试会话停止失败: ' + (responseData.error || '未知错误');

                // 显示错误通知
                if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
                    AIGDB.utils.showNotification(message, 'error');
                } else {
                    alert(message);
                }

                // 记录到调试日志
                if (debugLogger) {
                    debugLogger.log(message, 'error');
                }
            }
        }
    });

    // 监听错误消息
    wsManager.on('error', function(data) {
        console.error('收到WebSocket错误消息:', data);

        // 恢复按钮状态
        $('#start-debug-btn').prop('disabled', false).html('<i class="fas fa-play"></i> 开始调试');
        $('#stop-debug-btn').prop('disabled', true);

        const message = 'WebSocket错误: ' + (data.error || '未知错误');

        // 显示错误通知
        if (typeof AIGDB !== 'undefined' && AIGDB.utils && AIGDB.utils.showNotification) {
            AIGDB.utils.showNotification(message, 'error');
        }

        // 记录到调试日志
        if (debugLogger) {
            debugLogger.log(message, 'error');
        }
    });

    console.log('WebSocket消息处理器设置完成');
}

// 在页面加载时和WebSocket连接建立时设置处理器
setupWebSocketHandlers();

// 监听WebSocket连接事件
document.addEventListener('ws_connected', function() {
    console.log('WebSocket连接已建立，重新设置消息处理器');
    setupWebSocketHandlers();
});

// 调试工具：手动获取状态
function debugGetStatus() {
    console.log('手动获取状态...');
    if (window.wsManager) {
        wsManager.send('get_status', {});
    } else {
        console.error('WebSocket管理器未初始化');
    }
}

// 调试工具：显示当前状态
function debugShowCurrentStatus() {
    console.log('=== 当前状态信息 ===');
    console.log('调试状态元素:', $('#debug-status').html());
    console.log('开始按钮状态:', $('#start-debug-btn').prop('disabled'), $('#start-debug-btn').html());
    console.log('停止按钮状态:', $('#stop-debug-btn').prop('disabled'));
    console.log('进度条:', $('#debug-progress').css('width'), $('#debug-progress').text());
    console.log('WebSocket状态:', window.wsManager ? wsManager.getState() : '未初始化');
}

// 将调试函数暴露到全局
window.debugGetStatus = debugGetStatus;
window.debugShowCurrentStatus = debugShowCurrentStatus;

function appendDebugOutput(text, type = 'info') {
    const outputElement = $('#debug-output');
    const timestamp = new Date().toLocaleTimeString();
    const className = `gdb-${type}`;
    
    const entry = `<div class="${className}">[${timestamp}] ${text}</div>`;
    
    console.log('=== appendDebugOutput : ' + entry);
    if (outputElement.find('.text-muted').length > 0) {
        outputElement.html(entry);
    } else {
        outputElement.append(entry);
    }
    
    // 滚动到底部
    outputElement.scrollTop(outputElement[0].scrollHeight);
}

function updateVariableMonitor(variables) {
    const container = $('#variable-monitor');
    
    if (!variables || Object.keys(variables).length === 0) {
        container.html('<p class="text-muted">暂无变量信息</p>');
        return;
    }
    
    let html = '';
    for (const [name, variable] of Object.entries(variables)) {
        html += `
            <div class="variable-item">
                <span class="variable-name">${name}</span>
                <span class="variable-type">(${variable.type})</span>
                <br>
                <span class="variable-value">${variable.value}</span>
            </div>
        `;
    }
    
    container.html(html);
}
</script>
{% endblock %}
