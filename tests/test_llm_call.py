"""
代码解析器测试
"""

import sys
import os,asyncio
# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from pathlib import Path

from src.ai_agent.llm_client import LLMClient, LLMResponse, Message
from src.config.config import Config

async def test_llm_tool(config):
    llm_client = LLMClient(config)
    messages = [
        Message(role = "system", content="你是一个专业的密码学算法助手，精通各类国际密码算法和GM标准算法。\\n#密码算法步骤：1.用户需要提供密钥ID 2.如果用户未提供密钥ID，则可调用query_keys查询密钥列表，并使用第1个符合条件的密钥ID。如果密钥列表为空，则需要调用generate_key生成密钥。"),
        Message(role="user", content="使用密钥ID=1的sm4算法加密以下数据：明文 /nothink")
    ]
    tools = [
        {"type": "function", "function": {
            "name": "sm4_encrypt",
            "description": "使用密钥ID的sm4算法加密数据",
            "parameters": {
                "type": "object",
                "properties": {
                    "plaintext": {
                        "type": "string",
                        "description": "明文"
                    },
                    "key_id": {
                        "type": "integer",
                        "description": "密钥ID"
                    }
                },
                "required": ["plaintext","key_id"]
             }
            }
        }
    ]
    response = await llm_client.chat_completion(messages, tools)
    print(f"大模型响应: {response}")
def test_tool_parse():
    content = '<think>\n\n</think>\n\n<tool_call>\n{"name": "sm4_encrypt", "arguments": {"key_id": 1, "plaintext": "明文"}}\n</tool_call>'
    
    
# main方法
if __name__ == "__main__":
    config = Config.from_env()
    asyncio.run(test_llm_tool(config))